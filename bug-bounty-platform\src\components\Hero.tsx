import Link from 'next/link'
import { ArrowR<PERSON>, Shield, Users, Award, DollarSign } from 'lucide-react'

export default function Hero() {
  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
      {/* Background with gradient and animated elements */}
      <div className="absolute inset-0 bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
        <div className="absolute inset-0 opacity-40" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%236366f1' fill-opacity='0.05'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
        }}></div>

        {/* Floating elements */}
        <div className="absolute top-20 left-10 w-20 h-20 bg-gradient-to-r from-indigo-400 to-purple-400 rounded-full opacity-20 animate-pulse-slow"></div>
        <div className="absolute top-40 right-20 w-16 h-16 bg-gradient-to-r from-cyan-400 to-blue-400 rounded-full opacity-20 animate-pulse-slow" style={{animationDelay: '1s'}}></div>
        <div className="absolute bottom-40 left-20 w-12 h-12 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full opacity-20 animate-pulse-slow" style={{animationDelay: '2s'}}></div>
        <div className="absolute bottom-20 right-10 w-24 h-24 bg-gradient-to-r from-emerald-400 to-teal-400 rounded-full opacity-20 animate-pulse-slow" style={{animationDelay: '0.5s'}}></div>
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
        <div className="text-center animate-fade-in-up">
          <div className="mb-8">
            <span className="inline-flex items-center px-4 py-2 rounded-full text-sm font-medium bg-indigo-100 text-indigo-800 mb-6">
              🚀 Join 50,000+ Security Researchers Worldwide
            </span>
          </div>

          <h1 className="text-5xl md:text-7xl font-black text-gray-900 mb-8 leading-tight">
            Secure the Digital World
            <span className="block gradient-text mt-2">One Bug at a Time</span>
          </h1>

          <p className="text-xl md:text-2xl text-gray-600 mb-12 max-w-4xl mx-auto leading-relaxed">
            Join the world&apos;s largest community of ethical hackers and help organizations
            build more secure software. Discover vulnerabilities, earn rewards, and make
            the internet safer for everyone.
          </p>

          <div className="flex flex-col sm:flex-row gap-6 justify-center mb-16 animate-slide-in-right">
            <Link
              href="/signup"
              className="btn-primary text-lg px-10 py-4 flex items-center justify-center group"
            >
              Start Hacking
              <ArrowRight className="ml-3 h-5 w-5 group-hover:translate-x-1 transition-transform duration-200" />
            </Link>
            <Link
              href="/programs"
              className="btn-secondary text-lg px-10 py-4"
            >
              Browse Programs
            </Link>
          </div>

          {/* Hero Stats */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6 max-w-5xl mx-auto">
            <div className="text-center group">
              <div className="card card-hover p-8 bg-gradient-to-br from-white to-blue-50 border-blue-100">
                <div className="relative">
                  <Shield className="h-12 w-12 text-indigo-600 mx-auto mb-4 group-hover:scale-110 transition-transform duration-300" />
                  <div className="absolute inset-0 bg-indigo-600 rounded-full opacity-10 group-hover:opacity-20 transition-opacity duration-300 blur-xl"></div>
                </div>
                <div className="text-3xl font-black text-gray-900 mb-2">500+</div>
                <div className="text-gray-600 font-medium">Active Programs</div>
              </div>
            </div>
            <div className="text-center group">
              <div className="card card-hover p-8 bg-gradient-to-br from-white to-emerald-50 border-emerald-100">
                <div className="relative">
                  <Users className="h-12 w-12 text-emerald-600 mx-auto mb-4 group-hover:scale-110 transition-transform duration-300" />
                  <div className="absolute inset-0 bg-emerald-600 rounded-full opacity-10 group-hover:opacity-20 transition-opacity duration-300 blur-xl"></div>
                </div>
                <div className="text-3xl font-black text-gray-900 mb-2">50K+</div>
                <div className="text-gray-600 font-medium">Security Researchers</div>
              </div>
            </div>
            <div className="text-center group">
              <div className="card card-hover p-8 bg-gradient-to-br from-white to-purple-50 border-purple-100">
                <div className="relative">
                  <Award className="h-12 w-12 text-purple-600 mx-auto mb-4 group-hover:scale-110 transition-transform duration-300" />
                  <div className="absolute inset-0 bg-purple-600 rounded-full opacity-10 group-hover:opacity-20 transition-opacity duration-300 blur-xl"></div>
                </div>
                <div className="text-3xl font-black text-gray-900 mb-2">100K+</div>
                <div className="text-gray-600 font-medium">Vulnerabilities Found</div>
              </div>
            </div>
            <div className="text-center group">
              <div className="card card-hover p-8 bg-gradient-to-br from-white to-amber-50 border-amber-100">
                <div className="relative">
                  <DollarSign className="h-12 w-12 text-amber-600 mx-auto mb-4 group-hover:scale-110 transition-transform duration-300" />
                  <div className="absolute inset-0 bg-amber-600 rounded-full opacity-10 group-hover:opacity-20 transition-opacity duration-300 blur-xl"></div>
                </div>
                <div className="text-3xl font-black text-gray-900 mb-2">$50M+</div>
                <div className="text-gray-600 font-medium">Rewards Paid</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
