'use client'

import { useState } from 'react'
import {
  BarChart3,
  FileText,
  Users,
  Shield,
  TrendingUp,
  Clock,
  DollarSign
} from 'lucide-react'

export default function CompanyDashboardContent() {
  const [activeTab, setActiveTab] = useState('overview')

  const tabs = [
    { id: 'overview', name: 'Overview', icon: BarChart3 },
    { id: 'reports', name: 'Reports', icon: FileText },
    { id: 'programs', name: 'Programs', icon: Shield },
    { id: 'researchers', name: 'Researchers', icon: Users },
    { id: 'analytics', name: 'Analytics', icon: TrendingUp }
  ]

  const stats = [
    {
      title: 'Active Reports',
      value: '23',
      change: '+5 this week',
      changeType: 'positive',
      icon: FileText,
      color: 'text-blue-600 bg-blue-50'
    },
    {
      title: 'Total Bounties Paid',
      value: '$45,200',
      change: '+$8,500 this month',
      changeType: 'positive',
      icon: DollarSign,
      color: 'text-green-600 bg-green-50'
    },
    {
      title: 'Active Researchers',
      value: '156',
      change: '+12 this month',
      changeType: 'positive',
      icon: Users,
      color: 'text-purple-600 bg-purple-50'
    },
    {
      title: 'Avg Response Time',
      value: '2.3 days',
      change: '-0.5 days',
      changeType: 'positive',
      icon: Clock,
      color: 'text-orange-600 bg-orange-50'
    }
  ]

  const recentReports = [
    {
      id: 1,
      title: 'SQL Injection in Admin Panel',
      researcher: 'Alex Chen',
      severity: 'Critical',
      status: 'Triaging',
      submittedAt: '2 hours ago',
      bounty: '$5,000',
      statusColor: 'text-yellow-600 bg-yellow-50'
    },
    {
      id: 2,
      title: 'XSS in User Comments',
      researcher: 'Sarah Johnson',
      severity: 'High',
      status: 'Accepted',
      submittedAt: '1 day ago',
      bounty: '$2,500',
      statusColor: 'text-green-600 bg-green-50'
    },
    {
      id: 3,
      title: 'CSRF in Settings Page',
      researcher: 'Raj Patel',
      severity: 'Medium',
      status: 'Under Review',
      submittedAt: '3 days ago',
      bounty: 'Pending',
      statusColor: 'text-blue-600 bg-blue-50'
    }
  ]

  const programs = [
    {
      id: 1,
      name: 'Web Application Security',
      status: 'Active',
      participants: 89,
      reports: 156,
      bountyRange: '$500 - $10,000',
      lastActivity: '2 hours ago'
    },
    {
      id: 2,
      name: 'Mobile App Security',
      status: 'Active',
      participants: 45,
      reports: 67,
      bountyRange: '$300 - $5,000',
      lastActivity: '1 day ago'
    }
  ]

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'Critical': return 'text-red-600 bg-red-50'
      case 'High': return 'text-orange-600 bg-orange-50'
      case 'Medium': return 'text-yellow-600 bg-yellow-50'
      case 'Low': return 'text-blue-600 bg-blue-50'
      default: return 'text-gray-600 bg-gray-50'
    }
  }

  return (
    <div className="space-y-8">
      {/* Tabs */}
      <div className="flex flex-wrap gap-2">
        {tabs.map((tab) => {
          const IconComponent = tab.icon
          return (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`flex items-center space-x-2 px-6 py-3 rounded-xl font-medium transition-all duration-200 ${
                activeTab === tab.id
                  ? 'bg-gradient-to-r from-emerald-600 to-teal-600 text-white shadow-lg'
                  : 'bg-white text-gray-700 hover:bg-gray-50 border border-gray-200'
              }`}
            >
              <IconComponent className="h-5 w-5" />
              <span>{tab.name}</span>
            </button>
          )
        })}
      </div>

      {activeTab === 'overview' && (
        <div className="space-y-8">
          {/* Stats Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {stats.map((stat, index) => {
              const IconComponent = stat.icon
              return (
                <div key={index} className="card card-hover p-8 group bg-gradient-to-br from-white to-emerald-50 border-emerald-100">
                  <div className="flex items-center justify-between mb-4">
                    <div className={`inline-flex items-center justify-center w-12 h-12 rounded-xl ${stat.color}`}>
                      <IconComponent className="h-6 w-6" />
                    </div>
                    <span className={`text-sm font-medium ${
                      stat.changeType === 'positive' ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {stat.change}
                    </span>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-gray-500 mb-1">{stat.title}</h3>
                    <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                  </div>
                </div>
              )
            })}
          </div>

          {/* Recent Reports */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <div className="card p-8 bg-gradient-to-br from-white to-gray-50">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-bold text-gray-900">Recent Reports</h2>
                <button className="btn-ghost text-emerald-600">View All</button>
              </div>
              <div className="space-y-4">
                {recentReports.map((report) => (
                  <div key={report.id} className="border border-gray-200 rounded-xl p-4 hover:shadow-md transition-shadow">
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex-1">
                        <h3 className="font-semibold text-gray-900 mb-1">{report.title}</h3>
                        <p className="text-sm text-gray-600">by {report.researcher}</p>
                      </div>
                      <div className="text-right">
                        <div className="font-semibold text-gray-900">{report.bounty}</div>
                        <div className="text-xs text-gray-500">{report.submittedAt}</div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-3">
                      <span className={`px-2 py-1 text-xs rounded-full ${getSeverityColor(report.severity)}`}>
                        {report.severity}
                      </span>
                      <span className={`px-2 py-1 text-xs rounded-full ${report.statusColor}`}>
                        {report.status}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Active Programs */}
            <div className="card p-8 bg-gradient-to-br from-white to-gray-50">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-bold text-gray-900">Active Programs</h2>
                <button className="btn-ghost text-emerald-600">Manage</button>
              </div>
              <div className="space-y-4">
                {programs.map((program) => (
                  <div key={program.id} className="border border-gray-200 rounded-xl p-4 hover:shadow-md transition-shadow">
                    <div className="flex items-start justify-between mb-3">
                      <div>
                        <h3 className="font-semibold text-gray-900 mb-1">{program.name}</h3>
                        <p className="text-sm text-gray-600">{program.bountyRange}</p>
                      </div>
                      <span className="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">
                        {program.status}
                      </span>
                    </div>
                    <div className="grid grid-cols-3 gap-4 text-center">
                      <div>
                        <div className="text-lg font-bold text-gray-900">{program.participants}</div>
                        <div className="text-xs text-gray-500">Participants</div>
                      </div>
                      <div>
                        <div className="text-lg font-bold text-gray-900">{program.reports}</div>
                        <div className="text-xs text-gray-500">Reports</div>
                      </div>
                      <div>
                        <div className="text-xs text-gray-500">Last Activity</div>
                        <div className="text-xs font-medium text-gray-700">{program.lastActivity}</div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}

      {activeTab === 'reports' && (
        <div className="card p-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-6">All Reports</h2>
          <div className="text-center py-12">
            <FileText className="h-16 w-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Reports Management</h3>
            <p className="text-gray-600">Detailed reports management interface will be displayed here.</p>
          </div>
        </div>
      )}

      {activeTab === 'programs' && (
        <div className="card p-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-6">Program Management</h2>
          <div className="text-center py-12">
            <Shield className="h-16 w-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Program Settings</h3>
            <p className="text-gray-600">Program configuration and management tools will be displayed here.</p>
          </div>
        </div>
      )}

      {activeTab === 'researchers' && (
        <div className="card p-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-6">Researcher Management</h2>
          <div className="text-center py-12">
            <Users className="h-16 w-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Researcher Directory</h3>
            <p className="text-gray-600">Researcher profiles and interaction tools will be displayed here.</p>
          </div>
        </div>
      )}

      {activeTab === 'analytics' && (
        <div className="card p-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-6">Analytics & Insights</h2>
          <div className="text-center py-12">
            <TrendingUp className="h-16 w-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Security Analytics</h3>
            <p className="text-gray-600">Detailed analytics and reporting dashboard will be displayed here.</p>
          </div>
        </div>
      )}
    </div>
  )
}
