'use client'

import { useState } from 'react'
import { 
  Trophy, 
  Award, 
  Star, 
  Target,
  Zap,
  Shield,
  Crown,
  Medal,
  Flame,
  Lock,
  CheckCircle,
  Calendar
} from 'lucide-react'

export default function DashboardAchievements() {
  const [activeCategory, setActiveCategory] = useState('all')

  const achievements = [
    {
      id: 1,
      title: 'First Blood',
      description: 'Submit your first vulnerability report',
      icon: Target,
      category: 'milestone',
      rarity: 'common',
      earned: true,
      earnedDate: '2020-01-20',
      progress: 100,
      requirement: 'Submit 1 report',
      points: 100,
      color: 'text-blue-600 bg-blue-50'
    },
    {
      id: 2,
      title: 'Critical Hunter',
      description: 'Find 5 critical vulnerabilities',
      icon: Crown,
      category: 'severity',
      rarity: 'rare',
      earned: true,
      earnedDate: '2020-06-15',
      progress: 100,
      requirement: 'Find 5 critical bugs',
      points: 500,
      color: 'text-red-600 bg-red-50'
    },
    {
      id: 3,
      title: 'Speed Demon',
      description: 'Get a report accepted within 24 hours',
      icon: Zap,
      category: 'speed',
      rarity: 'uncommon',
      earned: true,
      earnedDate: '2020-03-10',
      progress: 100,
      requirement: 'Report accepted in <24h',
      points: 250,
      color: 'text-yellow-600 bg-yellow-50'
    },
    {
      id: 4,
      title: 'Hall of Fame',
      description: 'Get mentioned in a company\'s hall of fame',
      icon: Star,
      category: 'recognition',
      rarity: 'epic',
      earned: true,
      earnedDate: '2021-01-01',
      progress: 100,
      requirement: 'Hall of Fame mention',
      points: 1000,
      color: 'text-purple-600 bg-purple-50'
    },
    {
      id: 5,
      title: 'Bug Collector',
      description: 'Find 100 vulnerabilities',
      icon: Shield,
      category: 'milestone',
      rarity: 'rare',
      earned: true,
      earnedDate: '2021-08-15',
      progress: 100,
      requirement: 'Find 100 bugs',
      points: 750,
      color: 'text-green-600 bg-green-50'
    },
    {
      id: 6,
      title: 'Millionaire',
      description: 'Earn $1,000,000 in bounties',
      icon: Trophy,
      category: 'earnings',
      rarity: 'legendary',
      earned: false,
      earnedDate: null,
      progress: 12.5,
      requirement: 'Earn $1,000,000',
      points: 5000,
      color: 'text-amber-600 bg-amber-50'
    },
    {
      id: 7,
      title: 'Streak Master',
      description: 'Submit reports for 30 consecutive days',
      icon: Flame,
      category: 'consistency',
      rarity: 'uncommon',
      earned: false,
      earnedDate: null,
      progress: 67,
      requirement: '30 day streak',
      points: 300,
      color: 'text-orange-600 bg-orange-50'
    },
    {
      id: 8,
      title: 'Elite Researcher',
      description: 'Reach top 10 in global leaderboard',
      icon: Medal,
      category: 'ranking',
      rarity: 'epic',
      earned: false,
      earnedDate: null,
      progress: 85,
      requirement: 'Top 10 ranking',
      points: 2000,
      color: 'text-indigo-600 bg-indigo-50'
    }
  ]

  const categories = [
    { id: 'all', name: 'All Achievements', count: achievements.length },
    { id: 'milestone', name: 'Milestones', count: achievements.filter(a => a.category === 'milestone').length },
    { id: 'severity', name: 'Severity', count: achievements.filter(a => a.category === 'severity').length },
    { id: 'speed', name: 'Speed', count: achievements.filter(a => a.category === 'speed').length },
    { id: 'earnings', name: 'Earnings', count: achievements.filter(a => a.category === 'earnings').length },
    { id: 'recognition', name: 'Recognition', count: achievements.filter(a => a.category === 'recognition').length }
  ]

  const getRarityColor = (rarity: string) => {
    switch (rarity) {
      case 'common': return 'text-gray-600 bg-gray-100'
      case 'uncommon': return 'text-green-600 bg-green-100'
      case 'rare': return 'text-blue-600 bg-blue-100'
      case 'epic': return 'text-purple-600 bg-purple-100'
      case 'legendary': return 'text-yellow-600 bg-yellow-100'
      default: return 'text-gray-600 bg-gray-100'
    }
  }

  const filteredAchievements = achievements.filter(achievement => 
    activeCategory === 'all' || achievement.category === activeCategory
  )

  const earnedAchievements = achievements.filter(a => a.earned)
  const totalPoints = earnedAchievements.reduce((sum, a) => sum + a.points, 0)

  return (
    <div className="space-y-8">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-black text-gray-900">Achievements</h1>
        <p className="text-gray-600 mt-2">Track your progress and unlock rewards</p>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="card p-6 bg-gradient-to-br from-white to-yellow-50 border-yellow-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Points</p>
              <p className="text-2xl font-bold text-gray-900">{totalPoints.toLocaleString()}</p>
            </div>
            <Trophy className="h-8 w-8 text-yellow-600" />
          </div>
        </div>
        
        <div className="card p-6 bg-gradient-to-br from-white to-green-50 border-green-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Earned</p>
              <p className="text-2xl font-bold text-gray-900">{earnedAchievements.length}</p>
            </div>
            <CheckCircle className="h-8 w-8 text-green-600" />
          </div>
        </div>
        
        <div className="card p-6 bg-gradient-to-br from-white to-blue-50 border-blue-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">In Progress</p>
              <p className="text-2xl font-bold text-gray-900">
                {achievements.filter(a => !a.earned && a.progress > 0).length}
              </p>
            </div>
            <Target className="h-8 w-8 text-blue-600" />
          </div>
        </div>
        
        <div className="card p-6 bg-gradient-to-br from-white to-gray-50 border-gray-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Locked</p>
              <p className="text-2xl font-bold text-gray-900">
                {achievements.filter(a => !a.earned && a.progress === 0).length}
              </p>
            </div>
            <Lock className="h-8 w-8 text-gray-600" />
          </div>
        </div>
      </div>

      {/* Categories */}
      <div className="flex flex-wrap gap-2">
        {categories.map((category) => (
          <button
            key={category.id}
            onClick={() => setActiveCategory(category.id)}
            className={`px-4 py-2 rounded-lg font-medium transition-all duration-200 ${
              activeCategory === category.id
                ? 'bg-gradient-to-r from-indigo-600 to-purple-600 text-white shadow-lg'
                : 'bg-white text-gray-700 hover:bg-gray-50 border border-gray-200'
            }`}
          >
            {category.name} ({category.count})
          </button>
        ))}
      </div>

      {/* Achievements Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredAchievements.map((achievement) => {
          const IconComponent = achievement.icon
          return (
            <div 
              key={achievement.id} 
              className={`card card-hover p-6 relative overflow-hidden ${
                achievement.earned 
                  ? 'bg-gradient-to-br from-white to-gray-50' 
                  : 'bg-gradient-to-br from-gray-50 to-gray-100 opacity-75'
              }`}
            >
              {achievement.earned && (
                <div className="absolute top-4 right-4">
                  <CheckCircle className="h-6 w-6 text-green-600" />
                </div>
              )}
              
              <div className="flex items-start space-x-4 mb-4">
                <div className={`p-3 rounded-xl ${achievement.color} ${
                  achievement.earned ? '' : 'grayscale'
                }`}>
                  <IconComponent className="h-8 w-8" />
                </div>
                <div className="flex-1">
                  <div className="flex items-center space-x-2 mb-1">
                    <h3 className="font-bold text-gray-900">{achievement.title}</h3>
                    <span className={`px-2 py-1 text-xs rounded-full capitalize ${getRarityColor(achievement.rarity)}`}>
                      {achievement.rarity}
                    </span>
                  </div>
                  <p className="text-sm text-gray-600 mb-2">{achievement.description}</p>
                  <p className="text-xs text-gray-500">{achievement.requirement}</p>
                </div>
              </div>

              {!achievement.earned && achievement.progress > 0 && (
                <div className="mb-4">
                  <div className="flex items-center justify-between text-sm mb-1">
                    <span className="text-gray-600">Progress</span>
                    <span className="font-medium text-gray-900">{achievement.progress}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-indigo-600 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${achievement.progress}%` }}
                    ></div>
                  </div>
                </div>
              )}

              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Star className="h-4 w-4 text-yellow-500" />
                  <span className="text-sm font-medium text-gray-900">{achievement.points} points</span>
                </div>
                {achievement.earned && achievement.earnedDate && (
                  <div className="flex items-center space-x-1 text-xs text-gray-500">
                    <Calendar className="h-3 w-3" />
                    <span>{achievement.earnedDate}</span>
                  </div>
                )}
              </div>
            </div>
          )
        })}
      </div>

      {filteredAchievements.length === 0 && (
        <div className="text-center py-12">
          <Award className="h-16 w-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 mb-2">No achievements found</h3>
          <p className="text-gray-600">Try selecting a different category.</p>
        </div>
      )}

      {/* Recent Achievements */}
      {earnedAchievements.length > 0 && (
        <div className="card p-8 bg-gradient-to-br from-white to-indigo-50 border-indigo-100">
          <h2 className="text-xl font-bold text-gray-900 mb-6">Recent Achievements</h2>
          <div className="space-y-4">
            {earnedAchievements
              .sort((a, b) => new Date(b.earnedDate!).getTime() - new Date(a.earnedDate!).getTime())
              .slice(0, 3)
              .map((achievement) => {
                const IconComponent = achievement.icon
                return (
                  <div key={achievement.id} className="flex items-center space-x-4 p-4 bg-white rounded-lg border border-gray-100">
                    <div className={`p-2 rounded-lg ${achievement.color}`}>
                      <IconComponent className="h-6 w-6" />
                    </div>
                    <div className="flex-1">
                      <h3 className="font-semibold text-gray-900">{achievement.title}</h3>
                      <p className="text-sm text-gray-600">{achievement.description}</p>
                    </div>
                    <div className="text-right">
                      <div className="text-sm font-medium text-gray-900">+{achievement.points} points</div>
                      <div className="text-xs text-gray-500">{achievement.earnedDate}</div>
                    </div>
                  </div>
                )
              })}
          </div>
        </div>
      )}
    </div>
  )
}
