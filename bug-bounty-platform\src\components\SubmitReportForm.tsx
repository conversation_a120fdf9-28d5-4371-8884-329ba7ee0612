'use client'

import { useState } from 'react'
import {
  AlertTriangle,
  Shield,
  FileText,
  Send,
  Eye,
  Code
} from 'lucide-react'

export default function SubmitReportForm() {
  const [formData, setFormData] = useState({
    program: '',
    title: '',
    severity: '',
    category: '',
    description: '',
    stepsToReproduce: '',
    impact: '',
    proofOfConcept: '',
    attachments: []
  })

  const [currentStep, setCurrentStep] = useState(1)
  const totalSteps = 4

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    console.log('Report submitted:', formData)
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
  }

  const programs = [
    { id: 1, name: 'TechCorp Web Application', company: 'TechCorp' },
    { id: 2, name: 'CloudSecure Platform', company: 'CloudSecure' },
    { id: 3, name: 'MobileFirst App', company: 'MobileFirst' },
    { id: 4, name: 'DataVault System', company: 'DataVault' }
  ]

  const severityLevels = [
    { value: 'critical', label: 'Critical', color: 'text-red-600', description: 'Immediate threat to system security' },
    { value: 'high', label: 'High', color: 'text-orange-600', description: 'Significant security risk' },
    { value: 'medium', label: 'Medium', color: 'text-yellow-600', description: 'Moderate security concern' },
    { value: 'low', label: 'Low', color: 'text-blue-600', description: 'Minor security issue' }
  ]

  const categories = [
    { value: 'sql-injection', label: 'SQL Injection', icon: '💉' },
    { value: 'xss', label: 'Cross-Site Scripting (XSS)', icon: '🔗' },
    { value: 'csrf', label: 'Cross-Site Request Forgery', icon: '🔄' },
    { value: 'authentication', label: 'Authentication Bypass', icon: '🔐' },
    { value: 'authorization', label: 'Authorization Issues', icon: '👤' },
    { value: 'information-disclosure', label: 'Information Disclosure', icon: '📄' },
    { value: 'rce', label: 'Remote Code Execution', icon: '💻' },
    { value: 'other', label: 'Other', icon: '❓' }
  ]

  const renderStep = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className="space-y-8">
            <div className="text-center mb-8">
              <Shield className="h-16 w-16 text-indigo-600 mx-auto mb-4" />
              <h2 className="text-2xl font-bold text-gray-900 mb-2">Basic Information</h2>
              <p className="text-gray-600">Select the program and provide basic details about the vulnerability</p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-3">Select Program *</label>
              <select
                name="program"
                value={formData.program}
                onChange={handleChange}
                required
                className="input-field"
              >
                <option value="">Choose a program...</option>
                {programs.map((program) => (
                  <option key={program.id} value={program.id}>
                    {program.company} - {program.name}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-3">Vulnerability Title *</label>
              <input
                type="text"
                name="title"
                value={formData.title}
                onChange={handleChange}
                required
                placeholder="e.g., SQL Injection in User Profile Update"
                className="input-field"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-3">Severity Level *</label>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {severityLevels.map((severity) => (
                  <label key={severity.value} className="relative">
                    <input
                      type="radio"
                      name="severity"
                      value={severity.value}
                      onChange={handleChange}
                      className="sr-only peer"
                    />
                    <div className="card p-4 cursor-pointer border-2 border-gray-200 peer-checked:border-indigo-500 peer-checked:bg-indigo-50 hover:border-gray-300 transition-all">
                      <div className="flex items-center justify-between">
                        <div>
                          <div className={`font-semibold ${severity.color}`}>{severity.label}</div>
                          <div className="text-sm text-gray-600">{severity.description}</div>
                        </div>
                        <AlertTriangle className={`h-5 w-5 ${severity.color}`} />
                      </div>
                    </div>
                  </label>
                ))}
              </div>
            </div>
          </div>
        )

      case 2:
        return (
          <div className="space-y-8">
            <div className="text-center mb-8">
              <Code className="h-16 w-16 text-indigo-600 mx-auto mb-4" />
              <h2 className="text-2xl font-bold text-gray-900 mb-2">Vulnerability Details</h2>
              <p className="text-gray-600">Provide detailed information about the vulnerability</p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-3">Category *</label>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {categories.map((category) => (
                  <label key={category.value} className="relative">
                    <input
                      type="radio"
                      name="category"
                      value={category.value}
                      onChange={handleChange}
                      className="sr-only peer"
                    />
                    <div className="card p-4 cursor-pointer border-2 border-gray-200 peer-checked:border-indigo-500 peer-checked:bg-indigo-50 hover:border-gray-300 transition-all">
                      <div className="flex items-center space-x-3">
                        <span className="text-2xl">{category.icon}</span>
                        <span className="font-medium text-gray-900">{category.label}</span>
                      </div>
                    </div>
                  </label>
                ))}
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-3">Description *</label>
              <textarea
                name="description"
                value={formData.description}
                onChange={handleChange}
                required
                rows={6}
                placeholder="Provide a detailed description of the vulnerability..."
                className="input-field"
              />
            </div>
          </div>
        )

      case 3:
        return (
          <div className="space-y-8">
            <div className="text-center mb-8">
              <FileText className="h-16 w-16 text-indigo-600 mx-auto mb-4" />
              <h2 className="text-2xl font-bold text-gray-900 mb-2">Technical Details</h2>
              <p className="text-gray-600">Provide steps to reproduce and proof of concept</p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-3">Steps to Reproduce *</label>
              <textarea
                name="stepsToReproduce"
                value={formData.stepsToReproduce}
                onChange={handleChange}
                required
                rows={8}
                placeholder="1. Navigate to...&#10;2. Enter the following payload...&#10;3. Observe that..."
                className="input-field"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-3">Impact Assessment *</label>
              <textarea
                name="impact"
                value={formData.impact}
                onChange={handleChange}
                required
                rows={4}
                placeholder="Describe the potential impact of this vulnerability..."
                className="input-field"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-3">Proof of Concept</label>
              <textarea
                name="proofOfConcept"
                value={formData.proofOfConcept}
                onChange={handleChange}
                rows={6}
                placeholder="Provide code, screenshots, or other evidence..."
                className="input-field"
              />
            </div>
          </div>
        )

      case 4:
        return (
          <div className="space-y-8">
            <div className="text-center mb-8">
              <Eye className="h-16 w-16 text-indigo-600 mx-auto mb-4" />
              <h2 className="text-2xl font-bold text-gray-900 mb-2">Review & Submit</h2>
              <p className="text-gray-600">Review your report before submitting</p>
            </div>

            <div className="card p-6 bg-gray-50">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Report Summary</h3>
              <div className="space-y-3">
                <div><strong>Program:</strong> {programs.find(p => p.id.toString() === formData.program)?.name || 'Not selected'}</div>
                <div><strong>Title:</strong> {formData.title || 'Not provided'}</div>
                <div><strong>Severity:</strong> {formData.severity || 'Not selected'}</div>
                <div><strong>Category:</strong> {categories.find(c => c.value === formData.category)?.label || 'Not selected'}</div>
              </div>
            </div>

            <div className="bg-blue-50 border border-blue-200 rounded-xl p-6">
              <h4 className="font-semibold text-blue-900 mb-2">Before you submit:</h4>
              <ul className="text-sm text-blue-800 space-y-1">
                <li>• Ensure all required fields are completed</li>
                <li>• Double-check your steps to reproduce</li>
                <li>• Verify that this is a legitimate security vulnerability</li>
                <li>• Make sure you haven&apos;t violated any program rules</li>
              </ul>
            </div>
          </div>
        )

      default:
        return null
    }
  }

  return (
    <div className="card p-10 bg-gradient-to-br from-white to-red-50 border-red-100">
      {/* Progress Bar */}
      <div className="mb-8">
        <div className="flex items-center justify-between mb-4">
          <span className="text-sm font-medium text-gray-600">Step {currentStep} of {totalSteps}</span>
          <span className="text-sm font-medium text-gray-600">{Math.round((currentStep / totalSteps) * 100)}% Complete</span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div
            className="bg-gradient-to-r from-indigo-500 to-purple-500 h-2 rounded-full transition-all duration-300"
            style={{ width: `${(currentStep / totalSteps) * 100}%` }}
          ></div>
        </div>
      </div>

      <form onSubmit={handleSubmit}>
        {renderStep()}

        {/* Navigation Buttons */}
        <div className="flex justify-between mt-12">
          <button
            type="button"
            onClick={() => setCurrentStep(Math.max(1, currentStep - 1))}
            disabled={currentStep === 1}
            className="btn-secondary disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Previous
          </button>

          {currentStep < totalSteps ? (
            <button
              type="button"
              onClick={() => setCurrentStep(Math.min(totalSteps, currentStep + 1))}
              className="btn-primary"
            >
              Next Step
            </button>
          ) : (
            <button
              type="submit"
              className="btn-primary flex items-center space-x-2"
            >
              <Send className="h-5 w-5" />
              <span>Submit Report</span>
            </button>
          )}
        </div>
      </form>
    </div>
  )
}
