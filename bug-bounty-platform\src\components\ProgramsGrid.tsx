'use client'

import { useState } from 'react'
import Link from 'next/link'
import { ArrowRight, DollarSign, Users, Calendar, Star, Grid, List, Search } from 'lucide-react'

export default function ProgramsGrid() {
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')
  const [sortBy, setSortBy] = useState('newest')

  const programs = [
    {
      id: 1,
      company: 'TechCorp',
      logo: '🏢',
      title: 'Web Application Security',
      description: 'Find vulnerabilities in our main web application and API endpoints. We focus on critical security issues that could impact user data.',
      minBounty: 500,
      maxBounty: 10000,
      participants: 1250,
      lastUpdated: '2 days ago',
      rating: 4.8,
      tags: ['Web App', 'API', 'Critical'],
      difficulty: 'Medium',
      responseTime: '2-3 days',
      verified: true
    },
    {
      id: 2,
      company: 'CloudSecure',
      logo: '☁️',
      title: 'Cloud Infrastructure',
      description: 'Security assessment of our cloud infrastructure and services. Looking for configuration issues and access control vulnerabilities.',
      minBounty: 1000,
      maxBounty: 25000,
      participants: 890,
      lastUpdated: '1 day ago',
      rating: 4.9,
      tags: ['Cloud', 'Infrastructure', 'High'],
      difficulty: 'Hard',
      responseTime: '1-2 days',
      verified: true
    },
    {
      id: 3,
      company: 'MobileFirst',
      logo: '📱',
      title: 'Mobile Application',
      description: 'iOS and Android app security testing for our fintech platform. Focus on authentication, data storage, and API security.',
      minBounty: 300,
      maxBounty: 8000,
      participants: 2100,
      lastUpdated: '3 hours ago',
      rating: 4.7,
      tags: ['Mobile', 'iOS', 'Android'],
      difficulty: 'Easy',
      responseTime: '3-5 days',
      verified: true
    },
    {
      id: 4,
      company: 'DataVault',
      logo: '🔒',
      title: 'Database Security',
      description: 'Comprehensive security review of our database systems. Looking for SQL injection, privilege escalation, and data leakage issues.',
      minBounty: 800,
      maxBounty: 15000,
      participants: 650,
      lastUpdated: '5 hours ago',
      rating: 4.6,
      tags: ['Database', 'SQL', 'NoSQL'],
      difficulty: 'Medium',
      responseTime: '2-4 days',
      verified: true
    },
    {
      id: 5,
      company: 'CyberShield',
      logo: '🛡️',
      title: 'Network Security',
      description: 'Network infrastructure penetration testing. Focus on firewall configurations, network segmentation, and intrusion detection.',
      minBounty: 1200,
      maxBounty: 20000,
      participants: 420,
      lastUpdated: '1 day ago',
      rating: 4.5,
      tags: ['Network', 'Infrastructure', 'Pentesting'],
      difficulty: 'Hard',
      responseTime: '1-3 days',
      verified: true
    },
    {
      id: 6,
      company: 'BlockChain Inc',
      logo: '⛓️',
      title: 'Smart Contract Audit',
      description: 'Security audit of our DeFi smart contracts. Looking for reentrancy, overflow, and logic vulnerabilities.',
      minBounty: 2000,
      maxBounty: 50000,
      participants: 180,
      lastUpdated: '6 hours ago',
      rating: 4.9,
      tags: ['Blockchain', 'Smart Contracts', 'DeFi'],
      difficulty: 'Hard',
      responseTime: '1-2 days',
      verified: true
    }
  ]

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'Easy': return 'bg-green-100 text-green-800'
      case 'Medium': return 'bg-yellow-100 text-yellow-800'
      case 'Hard': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <div>
      {/* Header with search and controls */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 space-y-4 sm:space-y-0">
        <div className="flex items-center space-x-4">
          <div className="relative">
            <Search className="h-5 w-5 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2" />
            <input
              type="text"
              placeholder="Search programs..."
              className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent w-64"
            />
          </div>
          <select
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value)}
            className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="newest">Newest</option>
            <option value="bounty-high">Highest Bounty</option>
            <option value="bounty-low">Lowest Bounty</option>
            <option value="participants">Most Participants</option>
            <option value="rating">Highest Rated</option>
          </select>
        </div>
        
        <div className="flex items-center space-x-2">
          <button
            onClick={() => setViewMode('grid')}
            className={`p-2 rounded-lg ${viewMode === 'grid' ? 'bg-blue-100 text-blue-600' : 'text-gray-400 hover:text-gray-600'}`}
          >
            <Grid className="h-5 w-5" />
          </button>
          <button
            onClick={() => setViewMode('list')}
            className={`p-2 rounded-lg ${viewMode === 'list' ? 'bg-blue-100 text-blue-600' : 'text-gray-400 hover:text-gray-600'}`}
          >
            <List className="h-5 w-5" />
          </button>
        </div>
      </div>

      {/* Results count */}
      <div className="mb-6">
        <p className="text-gray-600">{programs.length} programs found</p>
      </div>

      {/* Programs Grid/List */}
      <div className={viewMode === 'grid' ? 'grid grid-cols-1 md:grid-cols-2 gap-6' : 'space-y-4'}>
        {programs.map((program) => (
          <div key={program.id} className={`bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow ${viewMode === 'list' ? 'flex items-center space-x-6' : ''}`}>
            <div className={viewMode === 'list' ? 'flex-1' : ''}>
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <div className="text-2xl">{program.logo}</div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900">{program.company}</h3>
                    <p className="text-sm text-gray-500">{program.title}</p>
                  </div>
                </div>
                <div className="flex items-center space-x-1">
                  <Star className="h-4 w-4 text-yellow-400 fill-current" />
                  <span className="text-sm text-gray-600">{program.rating}</span>
                  {program.verified && (
                    <div className="w-5 h-5 bg-blue-500 rounded-full flex items-center justify-center ml-2">
                      <span className="text-white text-xs">✓</span>
                    </div>
                  )}
                </div>
              </div>

              <p className="text-gray-600 mb-4">{program.description}</p>

              <div className="flex flex-wrap gap-2 mb-4">
                {program.tags.map((tag, index) => (
                  <span key={index} className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
                    {tag}
                  </span>
                ))}
                <span className={`px-2 py-1 text-xs rounded-full ${getDifficultyColor(program.difficulty)}`}>
                  {program.difficulty}
                </span>
              </div>

              <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                <div className="flex items-center space-x-4">
                  <div className="flex items-center space-x-1">
                    <DollarSign className="h-4 w-4" />
                    <span>${program.minBounty.toLocaleString()} - ${program.maxBounty.toLocaleString()}</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Users className="h-4 w-4" />
                    <span>{program.participants.toLocaleString()}</span>
                  </div>
                </div>
                <div className="flex items-center space-x-1">
                  <Calendar className="h-4 w-4" />
                  <span>{program.lastUpdated}</span>
                </div>
              </div>

              <div className="flex items-center justify-between">
                <div className="text-sm text-gray-500">
                  Response time: {program.responseTime}
                </div>
                <Link
                  href={`/programs/${program.id}`}
                  className="inline-flex items-center text-blue-600 hover:text-blue-700 font-medium"
                >
                  View Program
                  <ArrowRight className="ml-1 h-4 w-4" />
                </Link>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Pagination */}
      <div className="mt-8 flex justify-center">
        <nav className="flex items-center space-x-2">
          <button className="px-3 py-2 text-gray-500 hover:text-gray-700">Previous</button>
          <button className="px-3 py-2 bg-blue-600 text-white rounded">1</button>
          <button className="px-3 py-2 text-gray-500 hover:text-gray-700">2</button>
          <button className="px-3 py-2 text-gray-500 hover:text-gray-700">3</button>
          <button className="px-3 py-2 text-gray-500 hover:text-gray-700">Next</button>
        </nav>
      </div>
    </div>
  )
}
