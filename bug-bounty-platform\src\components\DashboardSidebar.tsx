'use client'

import { useState } from 'react'
import Link from 'next/link'
import { 
  BarChart3, 
  FileText, 
  Settings, 
  User, 
  Trophy, 
  Target,
  Bell,
  CreditCard,
  Shield,
  LogOut
} from 'lucide-react'

export default function DashboardSidebar() {
  const [activeItem, setActiveItem] = useState('overview')

  const menuItems = [
    { id: 'overview', name: 'Overview', icon: BarChart3, href: '/dashboard' },
    { id: 'reports', name: 'My Reports', icon: FileText, href: '/dashboard/reports' },
    { id: 'programs', name: 'Programs', icon: Shield, href: '/dashboard/programs' },
    { id: 'earnings', name: 'Earnings', icon: CreditCard, href: '/dashboard/earnings' },
    { id: 'achievements', name: 'Achievements', icon: Trophy, href: '/dashboard/achievements' },
    { id: 'profile', name: 'Profile', icon: User, href: '/dashboard/profile' },
    { id: 'notifications', name: 'Notifications', icon: Bell, href: '/dashboard/notifications' },
    { id: 'settings', name: 'Settings', icon: Settings, href: '/dashboard/settings' }
  ]

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      {/* User Profile Section */}
      <div className="text-center mb-6 pb-6 border-b border-gray-200">
        <div className="text-4xl mb-3">👨‍💻</div>
        <h3 className="text-lg font-semibold text-gray-900">Alex Chen</h3>
        <p className="text-sm text-gray-500">@alexsec</p>
        <div className="flex items-center justify-center space-x-4 mt-3">
          <div className="text-center">
            <div className="text-lg font-bold text-gray-900">342</div>
            <div className="text-xs text-gray-500">Bugs Found</div>
          </div>
          <div className="text-center">
            <div className="text-lg font-bold text-gray-900">$125K</div>
            <div className="text-xs text-gray-500">Total Earned</div>
          </div>
        </div>
        <div className="flex items-center justify-center space-x-1 mt-2">
          <span className="text-yellow-400">⭐</span>
          <span className="text-sm font-medium">9.8</span>
          <span className="text-xs text-gray-500">Reputation</span>
        </div>
      </div>

      {/* Navigation Menu */}
      <nav className="space-y-1">
        {menuItems.map((item) => {
          const IconComponent = item.icon
          return (
            <Link
              key={item.id}
              href={item.href}
              onClick={() => setActiveItem(item.id)}
              className={`flex items-center space-x-3 px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                activeItem === item.id
                  ? 'bg-blue-100 text-blue-700'
                  : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900'
              }`}
            >
              <IconComponent className="h-5 w-5" />
              <span>{item.name}</span>
            </Link>
          )
        })}
      </nav>

      {/* Logout Button */}
      <div className="mt-6 pt-6 border-t border-gray-200">
        <button className="flex items-center space-x-3 px-3 py-2 rounded-lg text-sm font-medium text-red-600 hover:bg-red-50 w-full">
          <LogOut className="h-5 w-5" />
          <span>Sign out</span>
        </button>
      </div>
    </div>
  )
}
