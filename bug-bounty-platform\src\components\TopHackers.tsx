import Link from 'next/link'
import { Trophy, Award, DollarSign, Target, ArrowRight } from 'lucide-react'

export default function TopHackers() {
  const hackers = [
    {
      id: 1,
      name: '<PERSON>',
      username: '@alexsec',
      avatar: '👨‍💻',
      rank: 1,
      totalEarnings: 125000,
      bugsFound: 342,
      reputation: 9.8,
      specialties: ['Web App', 'API', 'Mobile'],
      country: '🇺🇸',
      verified: true
    },
    {
      id: 2,
      name: '<PERSON>',
      username: '@sarahj',
      avatar: '👩‍💻',
      rank: 2,
      totalEarnings: 98500,
      bugsFound: 289,
      reputation: 9.7,
      specialties: ['Cloud', 'Infrastructure', 'DevOps'],
      country: '🇬🇧',
      verified: true
    },
    {
      id: 3,
      name: '<PERSON>',
      username: '@rajsec',
      avatar: '👨‍💻',
      rank: 3,
      totalEarnings: 87200,
      bugsFound: 256,
      reputation: 9.6,
      specialties: ['Database', 'Backend', 'Crypto'],
      country: '🇮🇳',
      verified: true
    },
    {
      id: 4,
      name: '<PERSON>',
      username: '@mariag',
      avatar: '👩‍💻',
      rank: 4,
      totalEarnings: 76800,
      bugsFound: 198,
      reputation: 9.5,
      specialties: ['Frontend', 'XSS', 'CSRF'],
      country: '🇪🇸',
      verified: true
    },
    {
      id: 5,
      name: 'David Kim',
      username: '@davidk',
      avatar: '👨‍💻',
      rank: 5,
      totalEarnings: 65400,
      bugsFound: 167,
      reputation: 9.4,
      specialties: ['Mobile', 'IoT', 'Hardware'],
      country: '🇰🇷',
      verified: true
    }
  ]

  const getRankIcon = (rank: number) => {
    switch (rank) {
      case 1: return <Trophy className="h-6 w-6 text-yellow-500" />
      case 2: return <Award className="h-6 w-6 text-gray-400" />
      case 3: return <Award className="h-6 w-6 text-amber-600" />
      default: return <span className="text-lg font-bold text-gray-600">#{rank}</span>
    }
  }

  return (
    <section className="py-16 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            Top Security Researchers
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Meet the elite hackers who are making the digital world safer.
            Join their ranks and start your journey to becoming a top researcher.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
          {/* Top 3 Featured */}
          <div className="space-y-6">
            {hackers.slice(0, 3).map((hacker) => (
              <div key={hacker.id} className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-6 border border-blue-100">
                <div className="flex items-center space-x-4">
                  <div className="flex items-center justify-center w-12 h-12 bg-white rounded-full shadow-sm">
                    {getRankIcon(hacker.rank)}
                  </div>
                  <div className="text-4xl">{hacker.avatar}</div>
                  <div className="flex-1">
                    <div className="flex items-center space-x-2">
                      <h3 className="text-lg font-semibold text-gray-900">{hacker.name}</h3>
                      <span className="text-sm text-gray-500">{hacker.username}</span>
                      <span className="text-lg">{hacker.country}</span>
                      {hacker.verified && (
                        <div className="w-5 h-5 bg-blue-500 rounded-full flex items-center justify-center">
                          <span className="text-white text-xs">✓</span>
                        </div>
                      )}
                    </div>
                    <div className="flex items-center space-x-4 mt-2 text-sm text-gray-600">
                      <div className="flex items-center space-x-1">
                        <DollarSign className="h-4 w-4" />
                        <span>${hacker.totalEarnings.toLocaleString()}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Target className="h-4 w-4" />
                        <span>{hacker.bugsFound} bugs</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <span>⭐ {hacker.reputation}</span>
                      </div>
                    </div>
                    <div className="flex flex-wrap gap-1 mt-3">
                      {hacker.specialties.map((specialty, index) => (
                        <span key={index} className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
                          {specialty}
                        </span>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Leaderboard */}
          <div className="bg-gray-50 rounded-lg p-6">
            <h3 className="text-xl font-semibold text-gray-900 mb-6">This Month&apos;s Leaderboard</h3>
            <div className="space-y-4">
              {hackers.map((hacker) => (
                <div key={hacker.id} className="flex items-center justify-between py-3 border-b border-gray-200 last:border-b-0">
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 flex items-center justify-center">
                      {getRankIcon(hacker.rank)}
                    </div>
                    <div className="text-2xl">{hacker.avatar}</div>
                    <div>
                      <div className="font-medium text-gray-900">{hacker.name}</div>
                      <div className="text-sm text-gray-500">{hacker.bugsFound} bugs found</div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="font-semibold text-gray-900">${hacker.totalEarnings.toLocaleString()}</div>
                    <div className="text-sm text-gray-500">⭐ {hacker.reputation}</div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        <div className="text-center">
          <Link
            href="/leaderboard"
            className="inline-flex items-center bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors"
          >
            View Full Leaderboard
            <ArrowRight className="ml-2 h-5 w-5" />
          </Link>
        </div>
      </div>
    </section>
  )
}
