'use client'

import { useState } from 'react'
import { Filter, ChevronDown, ChevronUp } from 'lucide-react'

export default function ProgramsFilters() {
  const [expandedSections, setExpandedSections] = useState({
    category: true,
    difficulty: true,
    bountyRange: true,
    company: true
  })

  const toggleSection = (section: keyof typeof expandedSections) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }))
  }

  const categories = [
    { name: 'Web Application', count: 156 },
    { name: 'Mobile Application', count: 89 },
    { name: 'API', count: 134 },
    { name: 'Cloud Infrastructure', count: 67 },
    { name: 'IoT', count: 23 },
    { name: 'Blockchain', count: 45 }
  ]

  const difficulties = [
    { name: 'Easy', count: 78, color: 'text-green-600' },
    { name: 'Medium', count: 145, color: 'text-yellow-600' },
    { name: 'Hard', count: 89, color: 'text-red-600' }
  ]

  const bountyRanges = [
    { name: '$100 - $1,000', count: 89 },
    { name: '$1,000 - $5,000', count: 134 },
    { name: '$5,000 - $10,000', count: 67 },
    { name: '$10,000+', count: 45 }
  ]

  const companies = [
    { name: 'TechCorp', count: 12 },
    { name: 'CloudSecure', count: 8 },
    { name: 'MobileFirst', count: 15 },
    { name: 'DataVault', count: 6 },
    { name: 'CyberShield', count: 9 }
  ]

  return (
    <div className="card p-8 bg-gradient-to-br from-white to-gray-50 border-gray-100 sticky top-24">
      <div className="flex items-center space-x-3 mb-8">
        <div className="p-2 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-xl">
          <Filter className="h-5 w-5 text-white" />
        </div>
        <h2 className="text-xl font-bold text-gray-900">Filters</h2>
      </div>

      {/* Category Filter */}
      <div className="mb-6">
        <button
          onClick={() => toggleSection('category')}
          className="flex items-center justify-between w-full text-left"
        >
          <h3 className="text-sm font-medium text-gray-900">Category</h3>
          {expandedSections.category ? (
            <ChevronUp className="h-4 w-4 text-gray-500" />
          ) : (
            <ChevronDown className="h-4 w-4 text-gray-500" />
          )}
        </button>
        {expandedSections.category && (
          <div className="mt-3 space-y-2">
            {categories.map((category) => (
              <label key={category.name} className="flex items-center">
                <input type="checkbox" className="rounded border-gray-300 text-blue-600 focus:ring-blue-500" />
                <span className="ml-2 text-sm text-gray-700">{category.name}</span>
                <span className="ml-auto text-xs text-gray-500">({category.count})</span>
              </label>
            ))}
          </div>
        )}
      </div>

      {/* Difficulty Filter */}
      <div className="mb-6">
        <button
          onClick={() => toggleSection('difficulty')}
          className="flex items-center justify-between w-full text-left"
        >
          <h3 className="text-sm font-medium text-gray-900">Difficulty</h3>
          {expandedSections.difficulty ? (
            <ChevronUp className="h-4 w-4 text-gray-500" />
          ) : (
            <ChevronDown className="h-4 w-4 text-gray-500" />
          )}
        </button>
        {expandedSections.difficulty && (
          <div className="mt-3 space-y-2">
            {difficulties.map((difficulty) => (
              <label key={difficulty.name} className="flex items-center">
                <input type="checkbox" className="rounded border-gray-300 text-blue-600 focus:ring-blue-500" />
                <span className={`ml-2 text-sm ${difficulty.color}`}>{difficulty.name}</span>
                <span className="ml-auto text-xs text-gray-500">({difficulty.count})</span>
              </label>
            ))}
          </div>
        )}
      </div>

      {/* Bounty Range Filter */}
      <div className="mb-6">
        <button
          onClick={() => toggleSection('bountyRange')}
          className="flex items-center justify-between w-full text-left"
        >
          <h3 className="text-sm font-medium text-gray-900">Bounty Range</h3>
          {expandedSections.bountyRange ? (
            <ChevronUp className="h-4 w-4 text-gray-500" />
          ) : (
            <ChevronDown className="h-4 w-4 text-gray-500" />
          )}
        </button>
        {expandedSections.bountyRange && (
          <div className="mt-3 space-y-2">
            {bountyRanges.map((range) => (
              <label key={range.name} className="flex items-center">
                <input type="checkbox" className="rounded border-gray-300 text-blue-600 focus:ring-blue-500" />
                <span className="ml-2 text-sm text-gray-700">{range.name}</span>
                <span className="ml-auto text-xs text-gray-500">({range.count})</span>
              </label>
            ))}
          </div>
        )}
      </div>

      {/* Company Filter */}
      <div className="mb-6">
        <button
          onClick={() => toggleSection('company')}
          className="flex items-center justify-between w-full text-left"
        >
          <h3 className="text-sm font-medium text-gray-900">Company</h3>
          {expandedSections.company ? (
            <ChevronUp className="h-4 w-4 text-gray-500" />
          ) : (
            <ChevronDown className="h-4 w-4 text-gray-500" />
          )}
        </button>
        {expandedSections.company && (
          <div className="mt-3 space-y-2">
            {companies.map((company) => (
              <label key={company.name} className="flex items-center">
                <input type="checkbox" className="rounded border-gray-300 text-blue-600 focus:ring-blue-500" />
                <span className="ml-2 text-sm text-gray-700">{company.name}</span>
                <span className="ml-auto text-xs text-gray-500">({company.count})</span>
              </label>
            ))}
          </div>
        )}
      </div>

      {/* Clear Filters */}
      <button className="w-full text-center text-sm text-blue-600 hover:text-blue-700 font-medium">
        Clear All Filters
      </button>
    </div>
  )
}
