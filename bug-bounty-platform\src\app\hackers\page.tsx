import Header from '@/components/Header'
import Footer from '@/components/Footer'
import HackersGrid from '@/components/HackersGrid'
import HackersFilters from '@/components/HackersFilters'

export default function HackersPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-purple-50">
      <Header />

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="mb-12 text-center">
          <h1 className="text-4xl md:text-5xl font-black text-gray-900 mb-6">
            Security <span className="gradient-text">Researchers</span>
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Discover talented security researchers and ethical hackers from around the world.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          <div className="lg:col-span-1">
            <HackersFilters />
          </div>
          <div className="lg:col-span-3">
            <HackersGrid />
          </div>
        </div>
      </main>

      <Footer />
    </div>
  )
}
