'use client'

import { useState } from 'react'
import Link from 'next/link'
import { 
  Search, 
  Grid, 
  List, 
  DollarSign, 
  Target, 
  Trophy, 
  Award,
  MapPin,
  Calendar,
  CheckCircle
} from 'lucide-react'

export default function HackersGrid() {
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')
  const [sortBy, setSortBy] = useState('reputation')

  const hackers = [
    {
      id: 1,
      name: '<PERSON>',
      username: '@alexsec',
      avatar: '👨‍💻',
      rank: 1,
      totalEarnings: 125000,
      bugsFound: 342,
      reputation: 9.8,
      specialties: ['Web App', 'API', 'Mobile'],
      country: '🇺🇸',
      location: 'San Francisco, CA',
      joinDate: 'Jan 2020',
      verified: true,
      bio: 'Senior security researcher with expertise in web application security and API testing.',
      recentActivity: '2 hours ago'
    },
    {
      id: 2,
      name: '<PERSON>',
      username: '@sarahj',
      avatar: '👩‍💻',
      rank: 2,
      totalEarnings: 98500,
      bugsFound: 289,
      reputation: 9.7,
      specialties: ['Cloud', 'Infrastructure', 'DevOps'],
      country: '🇬🇧',
      location: 'London, UK',
      joinDate: 'Mar 2019',
      verified: true,
      bio: 'Cloud security specialist focusing on AWS, Azure, and GCP infrastructure.',
      recentActivity: '1 day ago'
    },
    {
      id: 3,
      name: 'Raj Patel',
      username: '@rajsec',
      avatar: '👨‍💻',
      rank: 3,
      totalEarnings: 87200,
      bugsFound: 256,
      reputation: 9.6,
      specialties: ['Database', 'Backend', 'Crypto'],
      country: '🇮🇳',
      location: 'Mumbai, India',
      joinDate: 'Jul 2020',
      verified: true,
      bio: 'Database security expert with deep knowledge of SQL injection and NoSQL vulnerabilities.',
      recentActivity: '3 hours ago'
    },
    {
      id: 4,
      name: 'Maria Garcia',
      username: '@mariag',
      avatar: '👩‍💻',
      rank: 4,
      totalEarnings: 76800,
      bugsFound: 198,
      reputation: 9.5,
      specialties: ['Frontend', 'XSS', 'CSRF'],
      country: '🇪🇸',
      location: 'Madrid, Spain',
      joinDate: 'Nov 2019',
      verified: true,
      bio: 'Frontend security researcher specializing in client-side vulnerabilities.',
      recentActivity: '5 hours ago'
    },
    {
      id: 5,
      name: 'David Kim',
      username: '@davidk',
      avatar: '👨‍💻',
      rank: 5,
      totalEarnings: 65400,
      bugsFound: 167,
      reputation: 9.4,
      specialties: ['Mobile', 'IoT', 'Hardware'],
      country: '🇰🇷',
      location: 'Seoul, South Korea',
      joinDate: 'Feb 2021',
      verified: true,
      bio: 'Mobile and IoT security researcher with hardware hacking experience.',
      recentActivity: '1 day ago'
    },
    {
      id: 6,
      name: 'Emma Wilson',
      username: '@emmaw',
      avatar: '👩‍💻',
      rank: 6,
      totalEarnings: 54300,
      bugsFound: 145,
      reputation: 9.3,
      specialties: ['Network', 'Pentesting', 'Social Engineering'],
      country: '🇨🇦',
      location: 'Toronto, Canada',
      joinDate: 'Sep 2020',
      verified: true,
      bio: 'Network security specialist with extensive penetration testing experience.',
      recentActivity: '4 hours ago'
    }
  ]

  const getRankIcon = (rank: number) => {
    switch (rank) {
      case 1: return <Trophy className="h-5 w-5 text-yellow-500" />
      case 2: return <Award className="h-5 w-5 text-gray-400" />
      case 3: return <Award className="h-5 w-5 text-amber-600" />
      default: return <span className="text-sm font-bold text-gray-600">#{rank}</span>
    }
  }

  return (
    <div>
      {/* Header with search and controls */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 space-y-4 sm:space-y-0">
        <div className="flex items-center space-x-4">
          <div className="relative">
            <Search className="h-5 w-5 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2" />
            <input
              type="text"
              placeholder="Search researchers..."
              className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent w-64"
            />
          </div>
          <select
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value)}
            className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="reputation">Highest Reputation</option>
            <option value="earnings">Highest Earnings</option>
            <option value="bugs">Most Bugs Found</option>
            <option value="recent">Most Recent Activity</option>
          </select>
        </div>
        
        <div className="flex items-center space-x-2">
          <button
            onClick={() => setViewMode('grid')}
            className={`p-2 rounded-lg ${viewMode === 'grid' ? 'bg-blue-100 text-blue-600' : 'text-gray-400 hover:text-gray-600'}`}
          >
            <Grid className="h-5 w-5" />
          </button>
          <button
            onClick={() => setViewMode('list')}
            className={`p-2 rounded-lg ${viewMode === 'list' ? 'bg-blue-100 text-blue-600' : 'text-gray-400 hover:text-gray-600'}`}
          >
            <List className="h-5 w-5" />
          </button>
        </div>
      </div>

      {/* Results count */}
      <div className="mb-6">
        <p className="text-gray-600">{hackers.length} researchers found</p>
      </div>

      {/* Hackers Grid/List */}
      <div className={viewMode === 'grid' ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6' : 'space-y-4'}>
        {hackers.map((hacker) => (
          <div key={hacker.id} className={`bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow ${viewMode === 'list' ? 'flex items-center space-x-6' : ''}`}>
            <div className={viewMode === 'list' ? 'flex-1' : ''}>
              {/* Header */}
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <div className="relative">
                    <div className="text-3xl">{hacker.avatar}</div>
                    <div className="absolute -top-1 -right-1">
                      {getRankIcon(hacker.rank)}
                    </div>
                  </div>
                  <div>
                    <div className="flex items-center space-x-2">
                      <h3 className="text-lg font-semibold text-gray-900">{hacker.name}</h3>
                      {hacker.verified && (
                        <CheckCircle className="h-4 w-4 text-blue-500" />
                      )}
                    </div>
                    <p className="text-sm text-gray-500">{hacker.username}</p>
                    <div className="flex items-center space-x-1 mt-1">
                      <span className="text-lg">{hacker.country}</span>
                      <MapPin className="h-3 w-3 text-gray-400" />
                      <span className="text-xs text-gray-500">{hacker.location}</span>
                    </div>
                  </div>
                </div>
                <div className="text-right">
                  <div className="flex items-center space-x-1">
                    <span className="text-yellow-400">⭐</span>
                    <span className="text-sm font-medium">{hacker.reputation}</span>
                  </div>
                </div>
              </div>

              {/* Bio */}
              <p className="text-gray-600 text-sm mb-4">{hacker.bio}</p>

              {/* Specialties */}
              <div className="flex flex-wrap gap-1 mb-4">
                {hacker.specialties.map((specialty, index) => (
                  <span key={index} className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
                    {specialty}
                  </span>
                ))}
              </div>

              {/* Stats */}
              <div className="grid grid-cols-3 gap-4 mb-4 text-center">
                <div>
                  <div className="flex items-center justify-center space-x-1">
                    <DollarSign className="h-4 w-4 text-green-600" />
                    <span className="text-sm font-semibold text-gray-900">
                      ${(hacker.totalEarnings / 1000).toFixed(0)}K
                    </span>
                  </div>
                  <div className="text-xs text-gray-500">Earned</div>
                </div>
                <div>
                  <div className="flex items-center justify-center space-x-1">
                    <Target className="h-4 w-4 text-blue-600" />
                    <span className="text-sm font-semibold text-gray-900">{hacker.bugsFound}</span>
                  </div>
                  <div className="text-xs text-gray-500">Bugs Found</div>
                </div>
                <div>
                  <div className="flex items-center justify-center space-x-1">
                    <Calendar className="h-4 w-4 text-purple-600" />
                    <span className="text-sm font-semibold text-gray-900">{hacker.joinDate}</span>
                  </div>
                  <div className="text-xs text-gray-500">Joined</div>
                </div>
              </div>

              {/* Footer */}
              <div className="flex items-center justify-between">
                <div className="text-xs text-gray-500">
                  Active {hacker.recentActivity}
                </div>
                <Link
                  href={`/hackers/${hacker.id}`}
                  className="text-blue-600 hover:text-blue-700 text-sm font-medium"
                >
                  View Profile
                </Link>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Pagination */}
      <div className="mt-8 flex justify-center">
        <nav className="flex items-center space-x-2">
          <button className="px-3 py-2 text-gray-500 hover:text-gray-700">Previous</button>
          <button className="px-3 py-2 bg-blue-600 text-white rounded">1</button>
          <button className="px-3 py-2 text-gray-500 hover:text-gray-700">2</button>
          <button className="px-3 py-2 text-gray-500 hover:text-gray-700">3</button>
          <button className="px-3 py-2 text-gray-500 hover:text-gray-700">Next</button>
        </nav>
      </div>
    </div>
  )
}
