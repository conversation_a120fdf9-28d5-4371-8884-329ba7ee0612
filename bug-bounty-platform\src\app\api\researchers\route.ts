import { NextRequest, NextResponse } from 'next/server'

// Mock database for researchers
const researchers = [
  {
    id: 1,
    name: '<PERSON>',
    username: 'alexsec',
    email: '<EMAIL>',
    avatar: '👨‍💻',
    rank: 1,
    totalEarnings: 125000,
    bugsFound: 342,
    reputation: 9.8,
    specialties: ['Web App', 'API', 'Mobile'],
    country: '🇺🇸',
    location: 'San Francisco, CA',
    joinDate: '2020-01-15',
    verified: true,
    bio: 'Senior security researcher with expertise in web application security and API testing.',
    recentActivity: '2 hours ago',
    socialLinks: {
      github: 'https://github.com/alexsec',
      twitter: 'https://twitter.com/alexsec',
      linkedin: 'https://linkedin.com/in/alexsec'
    },
    achievements: [
      { name: 'First Blood', earned: true, date: '2020-01-20' },
      { name: '<PERSON> Hunter', earned: true, date: '2020-06-15' },
      { name: '<PERSON> Demon', earned: true, date: '2020-03-10' },
      { name: 'Hall of Fame', earned: true, date: '2021-01-01' }
    ],
    stats: {
      criticalBugs: 45,
      highBugs: 89,
      mediumBugs: 156,
      lowBugs: 52,
      averageResponseTime: '2.3 days',
      successRate: 0.87
    }
  },
  {
    id: 2,
    name: '<PERSON>',
    username: 'sarahj',
    email: '<EMAIL>',
    avatar: '👩‍💻',
    rank: 2,
    totalEarnings: 98500,
    bugsFound: 289,
    reputation: 9.7,
    specialties: ['Cloud', 'Infrastructure', 'DevOps'],
    country: '🇬🇧',
    location: 'London, UK',
    joinDate: '2019-03-20',
    verified: true,
    bio: 'Cloud security specialist focusing on AWS, Azure, and GCP infrastructure.',
    recentActivity: '1 day ago',
    socialLinks: {
      github: 'https://github.com/sarahj',
      twitter: 'https://twitter.com/sarahj',
      linkedin: 'https://linkedin.com/in/sarahj'
    },
    achievements: [
      { name: 'First Blood', earned: true, date: '2019-03-25' },
      { name: 'Critical Hunter', earned: true, date: '2019-08-10' },
      { name: 'Cloud Expert', earned: true, date: '2020-01-15' }
    ],
    stats: {
      criticalBugs: 38,
      highBugs: 76,
      mediumBugs: 134,
      lowBugs: 41,
      averageResponseTime: '1.8 days',
      successRate: 0.91
    }
  }
]

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const search = searchParams.get('search') || ''
    const country = searchParams.get('country') || ''
    const specialty = searchParams.get('specialty') || ''
    const minReputation = parseFloat(searchParams.get('minReputation') || '0')

    let filteredResearchers = researchers.filter(researcher => {
      const matchesSearch = search === '' || 
        researcher.name.toLowerCase().includes(search.toLowerCase()) ||
        researcher.username.toLowerCase().includes(search.toLowerCase()) ||
        researcher.bio.toLowerCase().includes(search.toLowerCase())
      
      const matchesCountry = country === '' || researcher.country === country
      const matchesSpecialty = specialty === '' || researcher.specialties.some(s => 
        s.toLowerCase().includes(specialty.toLowerCase())
      )
      const matchesReputation = researcher.reputation >= minReputation

      return matchesSearch && matchesCountry && matchesSpecialty && matchesReputation
    })

    // Sort by rank
    filteredResearchers.sort((a, b) => a.rank - b.rank)

    // Pagination
    const startIndex = (page - 1) * limit
    const endIndex = startIndex + limit
    const paginatedResearchers = filteredResearchers.slice(startIndex, endIndex)

    return NextResponse.json({
      success: true,
      data: paginatedResearchers,
      pagination: {
        page,
        limit,
        total: filteredResearchers.length,
        totalPages: Math.ceil(filteredResearchers.length / limit)
      }
    })

  } catch (error) {
    console.error('Researchers API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const researcherData = await request.json()

    // Validate required fields
    const requiredFields = ['name', 'username', 'email', 'bio', 'location']
    for (const field of requiredFields) {
      if (!researcherData[field]) {
        return NextResponse.json(
          { error: `${field} is required` },
          { status: 400 }
        )
      }
    }

    // Check if username or email already exists
    const existingResearcher = researchers.find(r => 
      r.username === researcherData.username || r.email === researcherData.email
    )
    
    if (existingResearcher) {
      return NextResponse.json(
        { error: 'Username or email already exists' },
        { status: 409 }
      )
    }

    // Create new researcher profile
    const newResearcher = {
      id: researchers.length + 1,
      ...researcherData,
      rank: researchers.length + 1,
      totalEarnings: 0,
      bugsFound: 0,
      reputation: 0,
      verified: false,
      joinDate: new Date().toISOString().split('T')[0],
      recentActivity: 'Just joined',
      achievements: [],
      stats: {
        criticalBugs: 0,
        highBugs: 0,
        mediumBugs: 0,
        lowBugs: 0,
        averageResponseTime: 'N/A',
        successRate: 0
      }
    }

    researchers.push(newResearcher)

    return NextResponse.json({
      success: true,
      message: 'Researcher profile created successfully',
      data: newResearcher
    }, { status: 201 })

  } catch (error) {
    console.error('Create researcher error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
