import { NextRequest, NextResponse } from 'next/server'

// Mock database for programs
const programs = [
  {
    id: 1,
    company: 'TechCorp',
    logo: '🏢',
    title: 'Web Application Security',
    description: 'Find vulnerabilities in our main web application and API endpoints. We focus on critical security issues that could impact user data.',
    minBounty: 500,
    maxBounty: 10000,
    participants: 1250,
    lastUpdated: '2 days ago',
    rating: 4.8,
    tags: ['Web App', 'API', 'Critical'],
    difficulty: 'Medium',
    responseTime: '2-3 days',
    verified: true,
    status: 'active',
    scope: [
      'https://app.techcorp.com/*',
      'https://api.techcorp.com/*',
      'https://admin.techcorp.com/*'
    ],
    outOfScope: [
      'https://blog.techcorp.com/*',
      'Social engineering attacks',
      'Physical attacks',
      'DoS/DDoS attacks'
    ],
    rewards: [
      { severity: 'Critical', amount: '$5,000 - $10,000', description: 'Remote code execution, SQL injection leading to data breach' },
      { severity: 'High', amount: '$2,000 - $5,000', description: 'Authentication bypass, privilege escalation' },
      { severity: 'Medium', amount: '$500 - $2,000', description: 'XSS, CSRF, information disclosure' },
      { severity: 'Low', amount: '$100 - $500', description: 'Minor security misconfigurations' }
    ],
    rules: [
      'Do not access, modify, or delete user data',
      'Do not perform attacks that could impact service availability',
      'Report vulnerabilities through our platform only',
      'Do not publicly disclose vulnerabilities before they are fixed',
      'Respect user privacy and data protection laws'
    ],
    contact: {
      email: '<EMAIL>',
      responseTime: '2-3 business days',
      timezone: 'PST (UTC-8)'
    },
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-15T00:00:00Z'
  },
  {
    id: 2,
    company: 'CloudSecure',
    logo: '☁️',
    title: 'Cloud Infrastructure',
    description: 'Security assessment of our cloud infrastructure and services. Looking for configuration issues and access control vulnerabilities.',
    minBounty: 1000,
    maxBounty: 25000,
    participants: 890,
    lastUpdated: '1 day ago',
    rating: 4.9,
    tags: ['Cloud', 'Infrastructure', 'High'],
    difficulty: 'Hard',
    responseTime: '1-2 days',
    verified: true,
    status: 'active',
    scope: [
      'https://cloud.cloudsecure.com/*',
      'https://api.cloudsecure.com/*',
      'AWS infrastructure components'
    ],
    outOfScope: [
      'Third-party integrations',
      'Customer data access',
      'Physical security'
    ],
    rewards: [
      { severity: 'Critical', amount: '$10,000 - $25,000', description: 'Infrastructure compromise, data breach' },
      { severity: 'High', amount: '$5,000 - $10,000', description: 'Privilege escalation, unauthorized access' },
      { severity: 'Medium', amount: '$1,000 - $5,000', description: 'Configuration issues, information disclosure' },
      { severity: 'Low', amount: '$500 - $1,000', description: 'Minor security improvements' }
    ],
    rules: [
      'Do not access customer data or environments',
      'Do not perform destructive testing',
      'Follow responsible disclosure guidelines',
      'Respect cloud provider terms of service'
    ],
    contact: {
      email: '<EMAIL>',
      responseTime: '1-2 business days',
      timezone: 'EST (UTC-5)'
    },
    createdAt: '2024-01-02T00:00:00Z',
    updatedAt: '2024-01-16T00:00:00Z'
  }
]

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const search = searchParams.get('search') || ''
    const difficulty = searchParams.get('difficulty') || ''
    const minBounty = parseInt(searchParams.get('minBounty') || '0')
    const maxBounty = parseInt(searchParams.get('maxBounty') || '999999')

    let filteredPrograms = programs.filter(program => {
      const matchesSearch = search === '' || 
        program.company.toLowerCase().includes(search.toLowerCase()) ||
        program.title.toLowerCase().includes(search.toLowerCase()) ||
        program.description.toLowerCase().includes(search.toLowerCase())
      
      const matchesDifficulty = difficulty === '' || program.difficulty === difficulty
      const matchesBounty = program.minBounty >= minBounty && program.maxBounty <= maxBounty

      return matchesSearch && matchesDifficulty && matchesBounty
    })

    // Pagination
    const startIndex = (page - 1) * limit
    const endIndex = startIndex + limit
    const paginatedPrograms = filteredPrograms.slice(startIndex, endIndex)

    return NextResponse.json({
      success: true,
      data: paginatedPrograms,
      pagination: {
        page,
        limit,
        total: filteredPrograms.length,
        totalPages: Math.ceil(filteredPrograms.length / limit)
      }
    })

  } catch (error) {
    console.error('Programs API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const programData = await request.json()

    // Validate required fields
    const requiredFields = ['company', 'title', 'description', 'minBounty', 'maxBounty']
    for (const field of requiredFields) {
      if (!programData[field]) {
        return NextResponse.json(
          { error: `${field} is required` },
          { status: 400 }
        )
      }
    }

    // Create new program
    const newProgram = {
      id: programs.length + 1,
      ...programData,
      participants: 0,
      rating: 0,
      verified: false,
      status: 'pending',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }

    programs.push(newProgram)

    return NextResponse.json({
      success: true,
      message: 'Program created successfully',
      data: newProgram
    }, { status: 201 })

  } catch (error) {
    console.error('Create program error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
