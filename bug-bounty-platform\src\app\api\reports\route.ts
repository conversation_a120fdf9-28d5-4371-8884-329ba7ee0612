import { NextRequest, NextResponse } from 'next/server'

// Mock database for reports
let reports = [
  {
    id: 1,
    title: 'SQL Injection in User Profile Update',
    description: 'Found SQL injection vulnerability in the user profile update functionality that allows unauthorized data access.',
    programId: 1,
    programName: 'TechCorp Web Application',
    researcherId: 1,
    researcherName: '<PERSON>',
    researcherUsername: 'alexsec',
    severity: 'Critical',
    category: 'sql-injection',
    status: 'Accepted',
    bounty: 5000,
    submittedAt: '2024-01-15T10:30:00Z',
    updatedAt: '2024-01-18T14:20:00Z',
    stepsToReproduce: [
      'Navigate to /profile/edit',
      'Intercept the update request',
      'Inject SQL payload in the name parameter',
      'Observe database error revealing sensitive information'
    ],
    impact: 'Attacker can access sensitive user data including passwords and personal information.',
    proofOfConcept: 'POST /api/profile/update\nContent-Type: application/json\n\n{"name": "test\' OR 1=1--", "email": "<EMAIL>"}',
    attachments: [],
    timeline: [
      { date: '2024-01-15T10:30:00Z', action: 'Report submitted', actor: '<PERSON>' },
      { date: '2024-01-15T11:00:00Z', action: 'Report triaged', actor: 'Security Team' },
      { date: '2024-01-16T09:00:00Z', action: 'Vulnerability confirmed', actor: 'Security Team' },
      { date: '2024-01-18T14:20:00Z', action: 'Report accepted', actor: 'Security Team' }
    ]
  },
  {
    id: 2,
    title: 'XSS in Comment System',
    description: 'Stored XSS vulnerability in the comment system allows execution of malicious scripts.',
    programId: 1,
    programName: 'TechCorp Web Application',
    researcherId: 2,
    researcherName: 'Sarah Johnson',
    researcherUsername: 'sarahj',
    severity: 'High',
    category: 'xss',
    status: 'Under Review',
    bounty: null,
    submittedAt: '2024-01-20T15:45:00Z',
    updatedAt: '2024-01-20T15:45:00Z',
    stepsToReproduce: [
      'Navigate to any article with comments',
      'Submit a comment with XSS payload',
      'Observe script execution when page loads'
    ],
    impact: 'Attacker can steal user sessions and perform actions on behalf of other users.',
    proofOfConcept: '<script>alert("XSS")</script>',
    attachments: [],
    timeline: [
      { date: '2024-01-20T15:45:00Z', action: 'Report submitted', actor: 'Sarah Johnson' }
    ]
  }
]

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const status = searchParams.get('status') || ''
    const severity = searchParams.get('severity') || ''
    const programId = searchParams.get('programId') || ''
    const researcherId = searchParams.get('researcherId') || ''

    let filteredReports = reports.filter(report => {
      const matchesStatus = status === '' || report.status === status
      const matchesSeverity = severity === '' || report.severity === severity
      const matchesProgram = programId === '' || report.programId.toString() === programId
      const matchesResearcher = researcherId === '' || report.researcherId.toString() === researcherId

      return matchesStatus && matchesSeverity && matchesProgram && matchesResearcher
    })

    // Sort by submission date (newest first)
    filteredReports.sort((a, b) => new Date(b.submittedAt).getTime() - new Date(a.submittedAt).getTime())

    // Pagination
    const startIndex = (page - 1) * limit
    const endIndex = startIndex + limit
    const paginatedReports = filteredReports.slice(startIndex, endIndex)

    return NextResponse.json({
      success: true,
      data: paginatedReports,
      pagination: {
        page,
        limit,
        total: filteredReports.length,
        totalPages: Math.ceil(filteredReports.length / limit)
      }
    })

  } catch (error) {
    console.error('Reports API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const reportData = await request.json()

    // Validate required fields
    const requiredFields = ['title', 'description', 'programId', 'severity', 'category', 'stepsToReproduce', 'impact']
    for (const field of requiredFields) {
      if (!reportData[field]) {
        return NextResponse.json(
          { error: `${field} is required` },
          { status: 400 }
        )
      }
    }

    // Create new report
    const newReport = {
      id: reports.length + 1,
      ...reportData,
      status: 'Submitted',
      bounty: null,
      submittedAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      timeline: [
        {
          date: new Date().toISOString(),
          action: 'Report submitted',
          actor: reportData.researcherName || 'Unknown'
        }
      ]
    }

    reports.push(newReport)

    return NextResponse.json({
      success: true,
      message: 'Report submitted successfully',
      data: newReport
    }, { status: 201 })

  } catch (error) {
    console.error('Submit report error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
