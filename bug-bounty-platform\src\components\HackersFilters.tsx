'use client'

import { useState } from 'react'
import { Filter, ChevronDown, ChevronUp } from 'lucide-react'

export default function HackersFilters() {
  const [expandedSections, setExpandedSections] = useState({
    location: true,
    specialties: true,
    experience: true,
    reputation: true
  })

  const toggleSection = (section: keyof typeof expandedSections) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }))
  }

  const locations = [
    { name: 'United States', count: 1250, flag: '🇺🇸' },
    { name: 'India', count: 890, flag: '🇮🇳' },
    { name: 'United Kingdom', count: 567, flag: '🇬🇧' },
    { name: 'Germany', count: 445, flag: '🇩🇪' },
    { name: 'Canada', count: 334, flag: '🇨🇦' },
    { name: 'Australia', count: 223, flag: '🇦🇺' }
  ]

  const specialties = [
    { name: 'Web Application', count: 2156 },
    { name: 'Mobile Security', count: 1289 },
    { name: 'API Security', count: 1834 },
    { name: 'Cloud Security', count: 967 },
    { name: 'Network Security', count: 723 },
    { name: 'Blockchain', count: 445 },
    { name: 'IoT Security', count: 323 },
    { name: 'Social Engineering', count: 234 }
  ]

  const experienceLevels = [
    { name: 'Beginner (0-1 years)', count: 1234 },
    { name: 'Intermediate (2-4 years)', count: 2156 },
    { name: 'Advanced (5-9 years)', count: 1567 },
    { name: 'Expert (10+ years)', count: 789 }
  ]

  const reputationRanges = [
    { name: '9.5 - 10.0', count: 234 },
    { name: '9.0 - 9.4', count: 567 },
    { name: '8.5 - 8.9', count: 890 },
    { name: '8.0 - 8.4', count: 1123 },
    { name: 'Below 8.0', count: 2890 }
  ]

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center space-x-2 mb-6">
        <Filter className="h-5 w-5 text-gray-600" />
        <h2 className="text-lg font-semibold text-gray-900">Filters</h2>
      </div>

      {/* Location Filter */}
      <div className="mb-6">
        <button
          onClick={() => toggleSection('location')}
          className="flex items-center justify-between w-full text-left"
        >
          <h3 className="text-sm font-medium text-gray-900">Location</h3>
          {expandedSections.location ? (
            <ChevronUp className="h-4 w-4 text-gray-500" />
          ) : (
            <ChevronDown className="h-4 w-4 text-gray-500" />
          )}
        </button>
        {expandedSections.location && (
          <div className="mt-3 space-y-2">
            {locations.map((location) => (
              <label key={location.name} className="flex items-center">
                <input type="checkbox" className="rounded border-gray-300 text-blue-600 focus:ring-blue-500" />
                <span className="ml-2 text-lg">{location.flag}</span>
                <span className="ml-2 text-sm text-gray-700">{location.name}</span>
                <span className="ml-auto text-xs text-gray-500">({location.count})</span>
              </label>
            ))}
          </div>
        )}
      </div>

      {/* Specialties Filter */}
      <div className="mb-6">
        <button
          onClick={() => toggleSection('specialties')}
          className="flex items-center justify-between w-full text-left"
        >
          <h3 className="text-sm font-medium text-gray-900">Specialties</h3>
          {expandedSections.specialties ? (
            <ChevronUp className="h-4 w-4 text-gray-500" />
          ) : (
            <ChevronDown className="h-4 w-4 text-gray-500" />
          )}
        </button>
        {expandedSections.specialties && (
          <div className="mt-3 space-y-2">
            {specialties.map((specialty) => (
              <label key={specialty.name} className="flex items-center">
                <input type="checkbox" className="rounded border-gray-300 text-blue-600 focus:ring-blue-500" />
                <span className="ml-2 text-sm text-gray-700">{specialty.name}</span>
                <span className="ml-auto text-xs text-gray-500">({specialty.count})</span>
              </label>
            ))}
          </div>
        )}
      </div>

      {/* Experience Filter */}
      <div className="mb-6">
        <button
          onClick={() => toggleSection('experience')}
          className="flex items-center justify-between w-full text-left"
        >
          <h3 className="text-sm font-medium text-gray-900">Experience Level</h3>
          {expandedSections.experience ? (
            <ChevronUp className="h-4 w-4 text-gray-500" />
          ) : (
            <ChevronDown className="h-4 w-4 text-gray-500" />
          )}
        </button>
        {expandedSections.experience && (
          <div className="mt-3 space-y-2">
            {experienceLevels.map((level) => (
              <label key={level.name} className="flex items-center">
                <input type="checkbox" className="rounded border-gray-300 text-blue-600 focus:ring-blue-500" />
                <span className="ml-2 text-sm text-gray-700">{level.name}</span>
                <span className="ml-auto text-xs text-gray-500">({level.count})</span>
              </label>
            ))}
          </div>
        )}
      </div>

      {/* Reputation Filter */}
      <div className="mb-6">
        <button
          onClick={() => toggleSection('reputation')}
          className="flex items-center justify-between w-full text-left"
        >
          <h3 className="text-sm font-medium text-gray-900">Reputation Score</h3>
          {expandedSections.reputation ? (
            <ChevronUp className="h-4 w-4 text-gray-500" />
          ) : (
            <ChevronDown className="h-4 w-4 text-gray-500" />
          )}
        </button>
        {expandedSections.reputation && (
          <div className="mt-3 space-y-2">
            {reputationRanges.map((range) => (
              <label key={range.name} className="flex items-center">
                <input type="checkbox" className="rounded border-gray-300 text-blue-600 focus:ring-blue-500" />
                <span className="ml-2 text-sm text-gray-700">⭐ {range.name}</span>
                <span className="ml-auto text-xs text-gray-500">({range.count})</span>
              </label>
            ))}
          </div>
        )}
      </div>

      {/* Clear Filters */}
      <button className="w-full text-center text-sm text-blue-600 hover:text-blue-700 font-medium">
        Clear All Filters
      </button>
    </div>
  )
}
