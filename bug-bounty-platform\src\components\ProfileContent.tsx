'use client'

import { useState } from 'react'
import {
  FileText,
  Trophy,
  Target,
  CheckCircle,
  XCircle,
  Clock,
  TrendingUp
} from 'lucide-react'

export default function ProfileContent() {
  const [activeTab, setActiveTab] = useState('reports')

  const tabs = [
    { id: 'reports', name: 'Reports', icon: FileText, count: 89 },
    { id: 'achievements', name: 'Achievements', icon: Trophy, count: 12 },
    { id: 'activity', name: 'Activity', icon: TrendingUp, count: null },
    { id: 'stats', name: 'Statistics', icon: Target, count: null }
  ]

  const reports = [
    {
      id: 1,
      title: 'SQL Injection in User Profile Update',
      program: 'TechCorp Web Application',
      severity: 'Critical',
      status: 'Resolved',
      bounty: '$5,000',
      submittedAt: '2024-01-15',
      resolvedAt: '2024-01-18',
      statusColor: 'text-green-600 bg-green-50',
      statusIcon: CheckCircle
    },
    {
      id: 2,
      title: 'XSS in Comment System',
      program: 'CloudSecure Platform',
      severity: 'High',
      status: 'Triaging',
      bounty: 'Pending',
      submittedAt: '2024-01-20',
      resolvedAt: null,
      statusColor: 'text-blue-600 bg-blue-50',
      statusIcon: Clock
    },
    {
      id: 3,
      title: 'CSRF in Settings Page',
      program: 'MobileFirst App',
      severity: 'Medium',
      status: 'Duplicate',
      bounty: '$0',
      submittedAt: '2024-01-10',
      resolvedAt: '2024-01-12',
      statusColor: 'text-red-600 bg-red-50',
      statusIcon: XCircle
    }
  ]

  const achievements = [
    {
      id: 1,
      title: 'First Blood',
      description: 'First vulnerability found',
      icon: '🎯',
      earned: true,
      earnedAt: '2020-01-15'
    },
    {
      id: 2,
      title: 'Critical Hunter',
      description: 'Found 10 critical vulnerabilities',
      icon: '🔥',
      earned: true,
      earnedAt: '2020-06-20'
    },
    {
      id: 3,
      title: 'Speed Demon',
      description: 'Reported vulnerability within 24 hours',
      icon: '⚡',
      earned: true,
      earnedAt: '2020-03-10'
    },
    {
      id: 4,
      title: 'Hall of Fame',
      description: 'Featured in company hall of fame',
      icon: '🏆',
      earned: true,
      earnedAt: '2021-01-01'
    },
    {
      id: 5,
      title: 'Consistency King',
      description: 'Report vulnerabilities for 30 consecutive days',
      icon: '👑',
      earned: false,
      earnedAt: null
    },
    {
      id: 6,
      title: 'Master Researcher',
      description: 'Reach $100K in total earnings',
      icon: '🎓',
      earned: true,
      earnedAt: '2023-08-15'
    }
  ]

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'Critical': return 'text-red-600 bg-red-50'
      case 'High': return 'text-orange-600 bg-orange-50'
      case 'Medium': return 'text-yellow-600 bg-yellow-50'
      case 'Low': return 'text-blue-600 bg-blue-50'
      default: return 'text-gray-600 bg-gray-50'
    }
  }

  return (
    <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
      {/* Sidebar */}
      <div className="lg:col-span-1">
        <div className="card p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Profile Sections</h3>
          <nav className="space-y-2">
            {tabs.map((tab) => {
              const IconComponent = tab.icon
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`w-full flex items-center justify-between px-4 py-3 rounded-xl text-left transition-all duration-200 ${
                    activeTab === tab.id
                      ? 'bg-gradient-to-r from-indigo-600 to-purple-600 text-white'
                      : 'text-gray-700 hover:bg-gray-100'
                  }`}
                >
                  <div className="flex items-center space-x-3">
                    <IconComponent className="h-5 w-5" />
                    <span className="font-medium">{tab.name}</span>
                  </div>
                  {tab.count && (
                    <span className={`text-sm px-2 py-1 rounded-full ${
                      activeTab === tab.id ? 'bg-white/20' : 'bg-gray-200 text-gray-600'
                    }`}>
                      {tab.count}
                    </span>
                  )}
                </button>
              )
            })}
          </nav>
        </div>
      </div>

      {/* Main Content */}
      <div className="lg:col-span-3">
        <div className="card">
          <div className="p-6 border-b border-gray-200">
            <h2 className="text-xl font-semibold text-gray-900">
              {tabs.find(tab => tab.id === activeTab)?.name}
            </h2>
          </div>

          <div className="p-6">
            {activeTab === 'reports' && (
              <div className="space-y-6">
                {reports.map((report) => {
                  const StatusIcon = report.statusIcon
                  return (
                    <div key={report.id} className="border border-gray-200 rounded-xl p-6 hover:shadow-md transition-shadow">
                      <div className="flex items-start justify-between mb-4">
                        <div className="flex-1">
                          <h3 className="text-lg font-semibold text-gray-900 mb-2">{report.title}</h3>
                          <p className="text-gray-600 mb-3">{report.program}</p>
                          <div className="flex items-center space-x-4">
                            <span className={`px-3 py-1 text-sm rounded-full ${getSeverityColor(report.severity)}`}>
                              {report.severity}
                            </span>
                            <div className={`flex items-center space-x-2 px-3 py-1 rounded-full ${report.statusColor}`}>
                              <StatusIcon className="h-4 w-4" />
                              <span className="text-sm font-medium">{report.status}</span>
                            </div>
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="text-lg font-semibold text-gray-900">{report.bounty}</div>
                          <div className="text-sm text-gray-500">
                            Submitted: {new Date(report.submittedAt).toLocaleDateString()}
                          </div>
                          {report.resolvedAt && (
                            <div className="text-sm text-gray-500">
                              Resolved: {new Date(report.resolvedAt).toLocaleDateString()}
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  )
                })}
              </div>
            )}

            {activeTab === 'achievements' && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {achievements.map((achievement) => (
                  <div key={achievement.id} className={`border-2 border-dashed rounded-xl p-6 ${
                    achievement.earned
                      ? 'border-green-200 bg-green-50'
                      : 'border-gray-200 bg-gray-50'
                  }`}>
                    <div className="text-center">
                      <div className="text-4xl mb-3">{achievement.icon}</div>
                      <h3 className={`text-lg font-semibold mb-2 ${
                        achievement.earned ? 'text-green-800' : 'text-gray-500'
                      }`}>
                        {achievement.title}
                      </h3>
                      <p className={`text-sm mb-3 ${
                        achievement.earned ? 'text-green-600' : 'text-gray-500'
                      }`}>
                        {achievement.description}
                      </p>
                      {achievement.earned && achievement.earnedAt && (
                        <div className="text-xs text-green-600">
                          Earned on {new Date(achievement.earnedAt).toLocaleDateString()}
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}

            {activeTab === 'activity' && (
              <div className="text-center py-12">
                <TrendingUp className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Activity Timeline</h3>
                <p className="text-gray-600">Recent activity and timeline will be displayed here.</p>
              </div>
            )}

            {activeTab === 'stats' && (
              <div className="text-center py-12">
                <Target className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Detailed Statistics</h3>
                <p className="text-gray-600">Comprehensive statistics and analytics will be displayed here.</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
