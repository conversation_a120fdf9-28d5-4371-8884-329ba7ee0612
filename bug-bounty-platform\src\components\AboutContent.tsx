import { Target, Heart, Zap, Users, Shield } from 'lucide-react'

export default function AboutContent() {
  const values = [
    {
      icon: Target,
      title: 'Mission-Driven',
      description: 'We believe in making the internet safer for everyone through collaborative security research.',
      color: 'text-blue-600 bg-blue-50'
    },
    {
      icon: Heart,
      title: 'Community First',
      description: 'Our platform is built by and for the security community, fostering collaboration and growth.',
      color: 'text-red-600 bg-red-50'
    },
    {
      icon: Zap,
      title: 'Innovation',
      description: 'We continuously evolve our platform with cutting-edge technology and security practices.',
      color: 'text-yellow-600 bg-yellow-50'
    },
    {
      icon: Users,
      title: 'Transparency',
      description: 'We maintain open communication and fair practices in all our interactions.',
      color: 'text-green-600 bg-green-50'
    }
  ]

  const team = [
    {
      name: '<PERSON>',
      role: 'CEO & Co-Founder',
      bio: 'Former security researcher with 15+ years in cybersecurity. Led security teams at major tech companies.',
      avatar: '👩‍💼',
      linkedin: '#'
    },
    {
      name: '<PERSON>',
      role: 'CTO & Co-Founder',
      bio: 'Security engineer and platform architect. Built scalable security systems for Fortune 500 companies.',
      avatar: '👨‍💻',
      linkedin: '#'
    },
    {
      name: 'Dr. <PERSON>',
      role: 'Head of Research',
      bio: 'PhD in Computer Security. Published researcher with expertise in vulnerability analysis and threat modeling.',
      avatar: '👩‍🔬',
      linkedin: '#'
    },
    {
      name: 'Alex Kim',
      role: 'Head of Community',
      bio: 'Former ethical hacker turned community builder. Passionate about fostering inclusive security communities.',
      avatar: '👨‍🎓',
      linkedin: '#'
    }
  ]

  const timeline = [
    {
      year: '2019',
      title: 'Company Founded',
      description: 'Started with a vision to democratize cybersecurity through crowdsourced security research.'
    },
    {
      year: '2020',
      title: 'First 1000 Researchers',
      description: 'Reached our first milestone of 1,000 registered security researchers on the platform.'
    },
    {
      year: '2021',
      title: 'Global Expansion',
      description: 'Expanded operations globally, supporting researchers and companies across 50+ countries.'
    },
    {
      year: '2022',
      title: '$10M in Bounties',
      description: 'Celebrated paying out over $10 million in bounties to security researchers worldwide.'
    },
    {
      year: '2023',
      title: 'Enterprise Platform',
      description: 'Launched enterprise-grade features for large organizations and government agencies.'
    },
    {
      year: '2024',
      title: 'AI-Powered Security',
      description: 'Integrated AI tools to help researchers find vulnerabilities faster and more efficiently.'
    }
  ]

  return (
    <div className="py-20">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Our Story */}
        <section className="mb-20">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-black text-gray-900 mb-6">
              Our <span className="gradient-text">Story</span>
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Founded by security researchers, for security researchers. We understand the challenges
              and opportunities in the cybersecurity landscape.
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div className="space-y-6">
              <h3 className="text-2xl font-bold text-gray-900">Building a Safer Digital World</h3>
              <p className="text-gray-600 leading-relaxed">
                In 2019, we recognized that traditional security approaches weren&apos;t keeping pace with
                evolving threats. Organizations needed a way to tap into the collective intelligence
                of the global security community.
              </p>
              <p className="text-gray-600 leading-relaxed">
                We created BugBounty to bridge this gap, providing a platform where security researchers
                could collaborate with organizations to identify and fix vulnerabilities before they
                could be exploited by malicious actors.
              </p>
              <p className="text-gray-600 leading-relaxed">
                Today, we&apos;re proud to be the trusted partner for hundreds of organizations and
                thousands of security researchers worldwide, making the internet safer one vulnerability at a time.
              </p>
            </div>
            <div className="card p-8 bg-gradient-to-br from-indigo-50 to-purple-50 border-indigo-100">
              <div className="text-center">
                <Shield className="h-24 w-24 text-indigo-600 mx-auto mb-6" />
                <h4 className="text-xl font-bold text-gray-900 mb-4">Our Mission</h4>
                <p className="text-gray-600">
                  To democratize cybersecurity by creating the world&apos;s most trusted platform
                  for collaborative security research and vulnerability disclosure.
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* Our Values */}
        <section className="mb-20">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-black text-gray-900 mb-6">
              Our <span className="gradient-text">Values</span>
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              The principles that guide everything we do and shape our platform&apos;s culture.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {values.map((value, index) => {
              const IconComponent = value.icon
              return (
                <div key={index} className="card card-hover p-8 text-center group bg-gradient-to-br from-white to-gray-50">
                  <div className={`inline-flex items-center justify-center w-16 h-16 rounded-2xl ${value.color} mb-6 group-hover:scale-110 transition-transform duration-300`}>
                    <IconComponent className="h-8 w-8" />
                  </div>
                  <h3 className="text-xl font-bold text-gray-900 mb-4">{value.title}</h3>
                  <p className="text-gray-600 leading-relaxed">{value.description}</p>
                </div>
              )
            })}
          </div>
        </section>

        {/* Timeline */}
        <section className="mb-20">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-black text-gray-900 mb-6">
              Our <span className="gradient-text">Journey</span>
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Key milestones in our mission to make the digital world safer.
            </p>
          </div>

          <div className="relative">
            <div className="absolute left-1/2 transform -translate-x-1/2 w-1 h-full bg-gradient-to-b from-indigo-500 to-purple-500 rounded-full"></div>
            <div className="space-y-12">
              {timeline.map((item, index) => (
                <div key={index} className={`flex items-center ${index % 2 === 0 ? 'flex-row' : 'flex-row-reverse'}`}>
                  <div className={`w-1/2 ${index % 2 === 0 ? 'pr-8 text-right' : 'pl-8 text-left'}`}>
                    <div className="card p-6 bg-gradient-to-br from-white to-indigo-50 border-indigo-100">
                      <div className="text-2xl font-black text-indigo-600 mb-2">{item.year}</div>
                      <h3 className="text-xl font-bold text-gray-900 mb-3">{item.title}</h3>
                      <p className="text-gray-600">{item.description}</p>
                    </div>
                  </div>
                  <div className="relative flex items-center justify-center w-8 h-8 bg-white border-4 border-indigo-500 rounded-full z-10">
                    <div className="w-3 h-3 bg-indigo-500 rounded-full"></div>
                  </div>
                  <div className="w-1/2"></div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Team */}
        <section>
          <div className="text-center mb-16">
            <h2 className="text-4xl font-black text-gray-900 mb-6">
              Meet Our <span className="gradient-text">Team</span>
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              The passionate individuals behind BugBounty, dedicated to making cybersecurity accessible to all.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {team.map((member, index) => (
              <div key={index} className="card card-hover p-8 text-center group bg-gradient-to-br from-white to-gray-50">
                <div className="text-6xl mb-6">{member.avatar}</div>
                <h3 className="text-xl font-bold text-gray-900 mb-2">{member.name}</h3>
                <div className="text-indigo-600 font-semibold mb-4">{member.role}</div>
                <p className="text-gray-600 text-sm leading-relaxed mb-6">{member.bio}</p>
                <a
                  href={member.linkedin}
                  className="inline-flex items-center text-indigo-600 hover:text-indigo-700 font-medium text-sm"
                >
                  Connect on LinkedIn
                </a>
              </div>
            ))}
          </div>
        </section>
      </div>
    </div>
  )
}
