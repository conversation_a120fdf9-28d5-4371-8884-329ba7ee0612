'use client'

import { useState } from 'react'
import { 
  Search, 
  HelpCircle, 
  Book, 
  Shield, 
  Users, 
  CreditCard,
  ChevronDown,
  ChevronRight,
  MessageCircle,
  Mail,
  Phone
} from 'lucide-react'

export default function HelpCenter() {
  const [searchQuery, setSearchQuery] = useState('')
  const [expandedFaq, setExpandedFaq] = useState<number | null>(null)

  const categories = [
    {
      icon: Book,
      title: 'Getting Started',
      description: 'Learn the basics of using our platform',
      articles: 12,
      color: 'text-blue-600 bg-blue-50'
    },
    {
      icon: Shield,
      title: 'Security Research',
      description: 'Guidelines for ethical hacking and reporting',
      articles: 18,
      color: 'text-green-600 bg-green-50'
    },
    {
      icon: Users,
      title: 'Account Management',
      description: 'Manage your profile and settings',
      articles: 8,
      color: 'text-purple-600 bg-purple-50'
    },
    {
      icon: CreditCard,
      title: 'Payments & Rewards',
      description: 'Understanding bounties and payouts',
      articles: 15,
      color: 'text-orange-600 bg-orange-50'
    }
  ]

  const faqs = [
    {
      question: 'How do I get started as a security researcher?',
      answer: 'To get started, create an account, complete your profile, and browse available programs. Start with programs that match your skill level and read their scope carefully before testing.'
    },
    {
      question: 'What types of vulnerabilities can I report?',
      answer: 'You can report various security vulnerabilities including SQL injection, XSS, CSRF, authentication bypasses, and more. Each program specifies what types of vulnerabilities they accept.'
    },
    {
      question: 'How long does it take to receive payment?',
      answer: 'Payment times vary by program, but typically range from 1-4 weeks after a vulnerability is accepted and fixed. Some programs offer faster payment options.'
    },
    {
      question: 'Can I test on any website or application?',
      answer: 'No, you can only test on applications and websites that have active bug bounty programs. Testing on unauthorized systems is illegal and against our terms of service.'
    },
    {
      question: 'What should I include in my vulnerability report?',
      answer: 'Include a clear description, steps to reproduce, impact assessment, and proof of concept. The more detailed and clear your report, the faster it will be processed.'
    },
    {
      question: 'How do I know if my report is a duplicate?',
      answer: 'Before submitting, search existing reports and check the program\'s known issues list. If you\'re unsure, submit anyway - duplicate reports help validate findings.'
    }
  ]

  const popularArticles = [
    'How to Write a Great Vulnerability Report',
    'Understanding CVSS Scoring',
    'Setting Up Your Testing Environment',
    'Responsible Disclosure Guidelines',
    'Payment Methods and Tax Information',
    'Building Your Security Research Portfolio'
  ]

  return (
    <div className="py-12">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Hero Section */}
        <div className="text-center mb-16">
          <h1 className="text-4xl md:text-5xl font-black text-gray-900 mb-6">
            Help <span className="gradient-text">Center</span>
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed mb-8">
            Find answers to your questions and get the support you need to succeed on our platform.
          </p>

          {/* Search Bar */}
          <div className="max-w-2xl mx-auto relative">
            <Search className="h-6 w-6 text-gray-400 absolute left-4 top-1/2 transform -translate-y-1/2" />
            <input
              type="text"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              placeholder="Search for help articles, guides, and FAQs..."
              className="w-full pl-12 pr-6 py-4 text-lg border border-gray-300 rounded-2xl focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white shadow-lg"
            />
          </div>
        </div>

        {/* Categories */}
        <section className="mb-16">
          <h2 className="text-2xl font-bold text-gray-900 mb-8 text-center">Browse by Category</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {categories.map((category, index) => {
              const IconComponent = category.icon
              return (
                <div key={index} className="card card-hover p-8 text-center group cursor-pointer bg-gradient-to-br from-white to-gray-50">
                  <div className={`inline-flex items-center justify-center w-16 h-16 rounded-2xl ${category.color} mb-6 group-hover:scale-110 transition-transform duration-300`}>
                    <IconComponent className="h-8 w-8" />
                  </div>
                  <h3 className="text-xl font-bold text-gray-900 mb-3">{category.title}</h3>
                  <p className="text-gray-600 mb-4">{category.description}</p>
                  <div className="text-sm text-blue-600 font-medium">
                    {category.articles} articles
                  </div>
                </div>
              )
            })}
          </div>
        </section>

        {/* Popular Articles */}
        <section className="mb-16">
          <h2 className="text-2xl font-bold text-gray-900 mb-8 text-center">Popular Articles</h2>
          <div className="card p-8 bg-gradient-to-br from-white to-blue-50 border-blue-100">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {popularArticles.map((article, index) => (
                <a
                  key={index}
                  href="#"
                  className="flex items-center space-x-3 p-4 rounded-lg hover:bg-blue-100 transition-colors group"
                >
                  <Book className="h-5 w-5 text-blue-600 group-hover:scale-110 transition-transform" />
                  <span className="text-gray-900 group-hover:text-blue-700">{article}</span>
                </a>
              ))}
            </div>
          </div>
        </section>

        {/* FAQ Section */}
        <section className="mb-16">
          <h2 className="text-2xl font-bold text-gray-900 mb-8 text-center">Frequently Asked Questions</h2>
          <div className="max-w-4xl mx-auto">
            <div className="space-y-4">
              {faqs.map((faq, index) => (
                <div key={index} className="card bg-gradient-to-br from-white to-gray-50">
                  <button
                    onClick={() => setExpandedFaq(expandedFaq === index ? null : index)}
                    className="w-full p-6 text-left flex items-center justify-between hover:bg-gray-50 transition-colors"
                  >
                    <h3 className="text-lg font-semibold text-gray-900 pr-4">{faq.question}</h3>
                    {expandedFaq === index ? (
                      <ChevronDown className="h-5 w-5 text-gray-500 flex-shrink-0" />
                    ) : (
                      <ChevronRight className="h-5 w-5 text-gray-500 flex-shrink-0" />
                    )}
                  </button>
                  {expandedFaq === index && (
                    <div className="px-6 pb-6">
                      <p className="text-gray-600 leading-relaxed">{faq.answer}</p>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Contact Support */}
        <section>
          <div className="card p-12 text-center bg-gradient-to-br from-indigo-50 to-purple-50 border-indigo-100">
            <HelpCircle className="h-16 w-16 text-indigo-600 mx-auto mb-6" />
            <h2 className="text-2xl font-bold text-gray-900 mb-4">Still Need Help?</h2>
            <p className="text-gray-600 mb-8 max-w-2xl mx-auto">
              Can&apos;t find what you&apos;re looking for? Our support team is here to help you with any questions or issues.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <a
                href="/contact"
                className="btn-primary flex items-center space-x-2"
              >
                <Mail className="h-5 w-5" />
                <span>Contact Support</span>
              </a>
              <button className="btn-secondary flex items-center space-x-2">
                <MessageCircle className="h-5 w-5" />
                <span>Live Chat</span>
              </button>
              <a
                href="tel:+15551234567"
                className="btn-ghost flex items-center space-x-2"
              >
                <Phone className="h-5 w-5" />
                <span>Call Us</span>
              </a>
            </div>
          </div>
        </section>
      </div>
    </div>
  )
}
