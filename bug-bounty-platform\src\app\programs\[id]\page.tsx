import Header from '@/components/Header'
import Footer from '@/components/Footer'
import ProgramDetails from '@/components/ProgramDetails'

interface ProgramPageProps {
  params: Promise<{
    id: string
  }>
}

export default async function ProgramPage({ params }: ProgramPageProps) {
  const { id } = await params

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      <ProgramDetails programId={id} />
      <Footer />
    </div>
  )
}
