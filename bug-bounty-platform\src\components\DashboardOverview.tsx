'use client'

import { useState } from 'react'
import {
  DollarSign,
  Target,
  TrendingUp,
  Clock,
  ArrowRight,
  Calendar,
  CheckCircle,
  AlertCircle,
  XCircle
} from 'lucide-react'

export default function DashboardOverview() {
  const [timeframe, setTimeframe] = useState('this-month')

  const stats = [
    {
      title: 'Total Earnings',
      value: '$125,000',
      change: '+12.5%',
      changeType: 'positive',
      icon: DollarSign,
      color: 'text-green-600 bg-green-50'
    },
    {
      title: 'Bugs Found',
      value: '342',
      change: '+8',
      changeType: 'positive',
      icon: Target,
      color: 'text-blue-600 bg-blue-50'
    },
    {
      title: 'This Month',
      value: '$8,500',
      change: '+23.1%',
      changeType: 'positive',
      icon: TrendingUp,
      color: 'text-purple-600 bg-purple-50'
    },
    {
      title: 'Avg Response',
      value: '2.3 days',
      change: '-0.5 days',
      changeType: 'positive',
      icon: Clock,
      color: 'text-orange-600 bg-orange-50'
    }
  ]

  const recentReports = [
    {
      id: 1,
      title: 'SQL Injection in User Profile',
      program: 'TechCorp',
      severity: 'Critical',
      status: 'Accepted',
      bounty: '$5,000',
      submittedAt: '2 days ago',
      statusColor: 'text-green-600 bg-green-50'
    },
    {
      id: 2,
      title: 'XSS in Comment System',
      program: 'CloudSecure',
      severity: 'High',
      status: 'Under Review',
      bounty: 'Pending',
      submittedAt: '5 days ago',
      statusColor: 'text-yellow-600 bg-yellow-50'
    },
    {
      id: 3,
      title: 'CSRF in Settings Page',
      program: 'MobileFirst',
      severity: 'Medium',
      status: 'Triaging',
      bounty: 'Pending',
      submittedAt: '1 week ago',
      statusColor: 'text-blue-600 bg-blue-50'
    },
    {
      id: 4,
      title: 'Information Disclosure',
      program: 'DataVault',
      severity: 'Low',
      status: 'Duplicate',
      bounty: '$0',
      submittedAt: '2 weeks ago',
      statusColor: 'text-red-600 bg-red-50'
    }
  ]

  const upcomingPrograms = [
    {
      id: 1,
      company: 'CyberShield',
      title: 'Network Security Assessment',
      startDate: 'Dec 15, 2024',
      bountyRange: '$1,000 - $20,000',
      participants: 420
    },
    {
      id: 2,
      company: 'BlockChain Inc',
      title: 'Smart Contract Audit',
      startDate: 'Dec 20, 2024',
      bountyRange: '$2,000 - $50,000',
      participants: 180
    }
  ]

  const achievements = [
    { name: 'First Bug', icon: '🎯', earned: true },
    { name: 'Critical Hunter', icon: '🔥', earned: true },
    { name: 'Speed Demon', icon: '⚡', earned: true },
    { name: 'Hall of Fame', icon: '🏆', earned: false }
  ]

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'Critical': return 'text-red-600 bg-red-50'
      case 'High': return 'text-orange-600 bg-orange-50'
      case 'Medium': return 'text-yellow-600 bg-yellow-50'
      case 'Low': return 'text-blue-600 bg-blue-50'
      default: return 'text-gray-600 bg-gray-50'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'Accepted': return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'Under Review': return <AlertCircle className="h-4 w-4 text-yellow-500" />
      case 'Triaging': return <Clock className="h-4 w-4 text-blue-500" />
      case 'Duplicate': return <XCircle className="h-4 w-4 text-red-500" />
      default: return <Clock className="h-4 w-4 text-gray-500" />
    }
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>
          <p className="text-gray-600">Welcome back, Alex! Here&apos;s your security research overview.</p>
        </div>
        <select
          value={timeframe}
          onChange={(e) => setTimeframe(e.target.value)}
          className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        >
          <option value="this-week">This Week</option>
          <option value="this-month">This Month</option>
          <option value="this-year">This Year</option>
          <option value="all-time">All Time</option>
        </select>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
        {stats.map((stat, index) => {
          const IconComponent = stat.icon
          return (
            <div key={index} className="card card-hover p-8 group bg-gradient-to-br from-white to-indigo-50 border-indigo-100">
              <div className="flex items-center justify-between">
                <div className={`inline-flex items-center justify-center w-12 h-12 rounded-lg ${stat.color}`}>
                  <IconComponent className="h-6 w-6" />
                </div>
                <span className={`text-sm font-medium ${
                  stat.changeType === 'positive' ? 'text-green-600' : 'text-red-600'
                }`}>
                  {stat.change}
                </span>
              </div>
              <div className="mt-4">
                <h3 className="text-sm font-medium text-gray-500">{stat.title}</h3>
                <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
              </div>
            </div>
          )
        })}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Recent Reports */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          <div className="p-6 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <h2 className="text-lg font-semibold text-gray-900">Recent Reports</h2>
              <button className="text-blue-600 hover:text-blue-700 text-sm font-medium flex items-center">
                View all
                <ArrowRight className="ml-1 h-4 w-4" />
              </button>
            </div>
          </div>
          <div className="divide-y divide-gray-200">
            {recentReports.map((report) => (
              <div key={report.id} className="p-6">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <h3 className="text-sm font-medium text-gray-900">{report.title}</h3>
                    <p className="text-sm text-gray-500">{report.program}</p>
                    <div className="flex items-center space-x-4 mt-2">
                      <span className={`px-2 py-1 text-xs rounded-full ${getSeverityColor(report.severity)}`}>
                        {report.severity}
                      </span>
                      <div className="flex items-center space-x-1">
                        {getStatusIcon(report.status)}
                        <span className="text-xs text-gray-500">{report.status}</span>
                      </div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-sm font-medium text-gray-900">{report.bounty}</div>
                    <div className="text-xs text-gray-500">{report.submittedAt}</div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Achievements & Upcoming */}
        <div className="space-y-6">
          {/* Achievements */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Recent Achievements</h2>
            <div className="grid grid-cols-2 gap-4">
              {achievements.map((achievement, index) => (
                <div key={index} className={`p-3 rounded-lg border-2 border-dashed ${
                  achievement.earned ? 'border-green-200 bg-green-50' : 'border-gray-200 bg-gray-50'
                }`}>
                  <div className="text-center">
                    <div className="text-2xl mb-1">{achievement.icon}</div>
                    <div className={`text-xs font-medium ${
                      achievement.earned ? 'text-green-700' : 'text-gray-500'
                    }`}>
                      {achievement.name}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Upcoming Programs */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Upcoming Programs</h2>
            <div className="space-y-4">
              {upcomingPrograms.map((program) => (
                <div key={program.id} className="border border-gray-200 rounded-lg p-4">
                  <h3 className="text-sm font-medium text-gray-900">{program.company}</h3>
                  <p className="text-xs text-gray-500">{program.title}</p>
                  <div className="flex items-center justify-between mt-2">
                    <div className="flex items-center space-x-2">
                      <Calendar className="h-3 w-3 text-gray-400" />
                      <span className="text-xs text-gray-500">{program.startDate}</span>
                    </div>
                    <span className="text-xs font-medium text-green-600">{program.bountyRange}</span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
