import Header from '@/components/Header'
import Footer from '@/components/Footer'
import LeaderboardTable from '@/components/LeaderboardTable'
import LeaderboardStats from '@/components/LeaderboardStats'

export default function LeaderboardPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-amber-50">
      <Header />

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="mb-12 text-center">
          <h1 className="text-4xl md:text-5xl font-black text-gray-900 mb-6">
            Global <span className="gradient-text">Leaderboard</span>
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Top security researchers ranked by their contributions to the bug bounty community.
          </p>
        </div>

        <LeaderboardStats />
        <LeaderboardTable />
      </main>

      <Footer />
    </div>
  )
}
