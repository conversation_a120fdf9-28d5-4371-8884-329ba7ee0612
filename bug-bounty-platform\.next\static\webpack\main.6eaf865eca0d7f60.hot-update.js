"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("main",{

/***/ "./node_modules/next/dist/shared/lib/router/router.js":
/*!************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/router/router.js ***!
  \************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("// tslint:disable:no-console\n\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    createKey: function() {\n        return createKey;\n    },\n    default: function() {\n        return Router;\n    },\n    matchesMiddleware: function() {\n        return matchesMiddleware;\n    }\n});\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"./node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _removetrailingslash = __webpack_require__(/*! ./utils/remove-trailing-slash */ \"./node_modules/next/dist/shared/lib/router/utils/remove-trailing-slash.js\");\nconst _routeloader = __webpack_require__(/*! ../../../client/route-loader */ \"./node_modules/next/dist/client/route-loader.js\");\nconst _script = __webpack_require__(/*! ../../../client/script */ \"./node_modules/next/dist/client/script.js\");\nconst _iserror = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! ../../../lib/is-error */ \"./node_modules/next/dist/lib/is-error.js\"));\nconst _denormalizepagepath = __webpack_require__(/*! ../page-path/denormalize-page-path */ \"./node_modules/next/dist/shared/lib/page-path/denormalize-page-path.js\");\nconst _normalizelocalepath = __webpack_require__(/*! ../i18n/normalize-locale-path */ \"./node_modules/next/dist/shared/lib/i18n/normalize-locale-path.js\");\nconst _mitt = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! ../mitt */ \"./node_modules/next/dist/shared/lib/mitt.js\"));\nconst _utils = __webpack_require__(/*! ../utils */ \"./node_modules/next/dist/shared/lib/utils.js\");\nconst _isdynamic = __webpack_require__(/*! ./utils/is-dynamic */ \"./node_modules/next/dist/shared/lib/router/utils/is-dynamic.js\");\nconst _parserelativeurl = __webpack_require__(/*! ./utils/parse-relative-url */ \"./node_modules/next/dist/shared/lib/router/utils/parse-relative-url.js\");\nconst _resolverewrites = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! ./utils/resolve-rewrites */ \"?506d\"));\nconst _routematcher = __webpack_require__(/*! ./utils/route-matcher */ \"./node_modules/next/dist/shared/lib/router/utils/route-matcher.js\");\nconst _routeregex = __webpack_require__(/*! ./utils/route-regex */ \"./node_modules/next/dist/shared/lib/router/utils/route-regex.js\");\nconst _formaturl = __webpack_require__(/*! ./utils/format-url */ \"./node_modules/next/dist/shared/lib/router/utils/format-url.js\");\nconst _detectdomainlocale = __webpack_require__(/*! ../../../client/detect-domain-locale */ \"./node_modules/next/dist/client/detect-domain-locale.js\");\nconst _parsepath = __webpack_require__(/*! ./utils/parse-path */ \"./node_modules/next/dist/shared/lib/router/utils/parse-path.js\");\nconst _addlocale = __webpack_require__(/*! ../../../client/add-locale */ \"./node_modules/next/dist/client/add-locale.js\");\nconst _removelocale = __webpack_require__(/*! ../../../client/remove-locale */ \"./node_modules/next/dist/client/remove-locale.js\");\nconst _removebasepath = __webpack_require__(/*! ../../../client/remove-base-path */ \"./node_modules/next/dist/client/remove-base-path.js\");\nconst _addbasepath = __webpack_require__(/*! ../../../client/add-base-path */ \"./node_modules/next/dist/client/add-base-path.js\");\nconst _hasbasepath = __webpack_require__(/*! ../../../client/has-base-path */ \"./node_modules/next/dist/client/has-base-path.js\");\nconst _resolvehref = __webpack_require__(/*! ../../../client/resolve-href */ \"./node_modules/next/dist/client/resolve-href.js\");\nconst _isapiroute = __webpack_require__(/*! ../../../lib/is-api-route */ \"./node_modules/next/dist/lib/is-api-route.js\");\nconst _getnextpathnameinfo = __webpack_require__(/*! ./utils/get-next-pathname-info */ \"./node_modules/next/dist/shared/lib/router/utils/get-next-pathname-info.js\");\nconst _formatnextpathnameinfo = __webpack_require__(/*! ./utils/format-next-pathname-info */ \"./node_modules/next/dist/shared/lib/router/utils/format-next-pathname-info.js\");\nconst _comparestates = __webpack_require__(/*! ./utils/compare-states */ \"./node_modules/next/dist/shared/lib/router/utils/compare-states.js\");\nconst _islocalurl = __webpack_require__(/*! ./utils/is-local-url */ \"./node_modules/next/dist/shared/lib/router/utils/is-local-url.js\");\nconst _isbot = __webpack_require__(/*! ./utils/is-bot */ \"./node_modules/next/dist/shared/lib/router/utils/is-bot.js\");\nconst _omit = __webpack_require__(/*! ./utils/omit */ \"./node_modules/next/dist/shared/lib/router/utils/omit.js\");\nconst _interpolateas = __webpack_require__(/*! ./utils/interpolate-as */ \"./node_modules/next/dist/shared/lib/router/utils/interpolate-as.js\");\nconst _handlesmoothscroll = __webpack_require__(/*! ./utils/handle-smooth-scroll */ \"./node_modules/next/dist/shared/lib/router/utils/handle-smooth-scroll.js\");\nconst _constants = __webpack_require__(/*! ../../../lib/constants */ \"./node_modules/next/dist/lib/constants.js\");\nfunction buildCancellationError() {\n    return Object.assign(new Error('Route Cancelled'), {\n        cancelled: true\n    });\n}\nasync function matchesMiddleware(options) {\n    const matchers = await Promise.resolve(options.router.pageLoader.getMiddleware());\n    if (!matchers) return false;\n    const { pathname: asPathname } = (0, _parsepath.parsePath)(options.asPath);\n    // remove basePath first since path prefix has to be in the order of `/${basePath}/${locale}`\n    const cleanedAs = (0, _hasbasepath.hasBasePath)(asPathname) ? (0, _removebasepath.removeBasePath)(asPathname) : asPathname;\n    const asWithBasePathAndLocale = (0, _addbasepath.addBasePath)((0, _addlocale.addLocale)(cleanedAs, options.locale));\n    // Check only path match on client. Matching \"has\" should be done on server\n    // where we can access more info such as headers, HttpOnly cookie, etc.\n    return matchers.some((m)=>new RegExp(m.regexp).test(asWithBasePathAndLocale));\n}\nfunction stripOrigin(url) {\n    const origin = (0, _utils.getLocationOrigin)();\n    return url.startsWith(origin) ? url.substring(origin.length) : url;\n}\nfunction prepareUrlAs(router, url, as) {\n    // If url and as provided as an object representation,\n    // we'll format them into the string version here.\n    let [resolvedHref, resolvedAs] = (0, _resolvehref.resolveHref)(router, url, true);\n    const origin = (0, _utils.getLocationOrigin)();\n    const hrefWasAbsolute = resolvedHref.startsWith(origin);\n    const asWasAbsolute = resolvedAs && resolvedAs.startsWith(origin);\n    resolvedHref = stripOrigin(resolvedHref);\n    resolvedAs = resolvedAs ? stripOrigin(resolvedAs) : resolvedAs;\n    const preparedUrl = hrefWasAbsolute ? resolvedHref : (0, _addbasepath.addBasePath)(resolvedHref);\n    const preparedAs = as ? stripOrigin((0, _resolvehref.resolveHref)(router, as)) : resolvedAs || resolvedHref;\n    return {\n        url: preparedUrl,\n        as: asWasAbsolute ? preparedAs : (0, _addbasepath.addBasePath)(preparedAs)\n    };\n}\nfunction resolveDynamicRoute(pathname, pages) {\n    const cleanPathname = (0, _removetrailingslash.removeTrailingSlash)((0, _denormalizepagepath.denormalizePagePath)(pathname));\n    if (cleanPathname === '/404' || cleanPathname === '/_error') {\n        return pathname;\n    }\n    // handle resolving href for dynamic routes\n    if (!pages.includes(cleanPathname)) {\n        // eslint-disable-next-line array-callback-return\n        pages.some((page)=>{\n            if ((0, _isdynamic.isDynamicRoute)(page) && (0, _routeregex.getRouteRegex)(page).re.test(cleanPathname)) {\n                pathname = page;\n                return true;\n            }\n        });\n    }\n    return (0, _removetrailingslash.removeTrailingSlash)(pathname);\n}\nfunction getMiddlewareData(source, response, options) {\n    const nextConfig = {\n        basePath: options.router.basePath,\n        i18n: {\n            locales: options.router.locales\n        },\n        trailingSlash: Boolean(false)\n    };\n    const rewriteHeader = response.headers.get('x-nextjs-rewrite');\n    let rewriteTarget = rewriteHeader || response.headers.get('x-nextjs-matched-path');\n    const matchedPath = response.headers.get(_constants.MATCHED_PATH_HEADER);\n    if (matchedPath && !rewriteTarget && !matchedPath.includes('__next_data_catchall') && !matchedPath.includes('/_error') && !matchedPath.includes('/404')) {\n        // leverage x-matched-path to detect next.config.js rewrites\n        rewriteTarget = matchedPath;\n    }\n    if (rewriteTarget) {\n        if (rewriteTarget.startsWith('/') || false) {\n            const parsedRewriteTarget = (0, _parserelativeurl.parseRelativeUrl)(rewriteTarget);\n            const pathnameInfo = (0, _getnextpathnameinfo.getNextPathnameInfo)(parsedRewriteTarget.pathname, {\n                nextConfig,\n                parseData: true\n            });\n            let fsPathname = (0, _removetrailingslash.removeTrailingSlash)(pathnameInfo.pathname);\n            return Promise.all([\n                options.router.pageLoader.getPageList(),\n                (0, _routeloader.getClientBuildManifest)()\n            ]).then((param)=>{\n                let [pages, { __rewrites: rewrites }] = param;\n                let as = (0, _addlocale.addLocale)(pathnameInfo.pathname, pathnameInfo.locale);\n                if ((0, _isdynamic.isDynamicRoute)(as) || !rewriteHeader && pages.includes((0, _normalizelocalepath.normalizeLocalePath)((0, _removebasepath.removeBasePath)(as), options.router.locales).pathname)) {\n                    const parsedSource = (0, _getnextpathnameinfo.getNextPathnameInfo)((0, _parserelativeurl.parseRelativeUrl)(source).pathname, {\n                        nextConfig:  false ? 0 : nextConfig,\n                        parseData: true\n                    });\n                    as = (0, _addbasepath.addBasePath)(parsedSource.pathname);\n                    parsedRewriteTarget.pathname = as;\n                }\n                if (false) {} else if (!pages.includes(fsPathname)) {\n                    const resolvedPathname = resolveDynamicRoute(fsPathname, pages);\n                    if (resolvedPathname !== fsPathname) {\n                        fsPathname = resolvedPathname;\n                    }\n                }\n                const resolvedHref = !pages.includes(fsPathname) ? resolveDynamicRoute((0, _normalizelocalepath.normalizeLocalePath)((0, _removebasepath.removeBasePath)(parsedRewriteTarget.pathname), options.router.locales).pathname, pages) : fsPathname;\n                if ((0, _isdynamic.isDynamicRoute)(resolvedHref)) {\n                    const matches = (0, _routematcher.getRouteMatcher)((0, _routeregex.getRouteRegex)(resolvedHref))(as);\n                    Object.assign(parsedRewriteTarget.query, matches || {});\n                }\n                return {\n                    type: 'rewrite',\n                    parsedAs: parsedRewriteTarget,\n                    resolvedHref\n                };\n            });\n        }\n        const src = (0, _parsepath.parsePath)(source);\n        const pathname = (0, _formatnextpathnameinfo.formatNextPathnameInfo)({\n            ...(0, _getnextpathnameinfo.getNextPathnameInfo)(src.pathname, {\n                nextConfig,\n                parseData: true\n            }),\n            defaultLocale: options.router.defaultLocale,\n            buildId: ''\n        });\n        return Promise.resolve({\n            type: 'redirect-external',\n            destination: \"\" + pathname + src.query + src.hash\n        });\n    }\n    const redirectTarget = response.headers.get('x-nextjs-redirect');\n    if (redirectTarget) {\n        if (redirectTarget.startsWith('/')) {\n            const src = (0, _parsepath.parsePath)(redirectTarget);\n            const pathname = (0, _formatnextpathnameinfo.formatNextPathnameInfo)({\n                ...(0, _getnextpathnameinfo.getNextPathnameInfo)(src.pathname, {\n                    nextConfig,\n                    parseData: true\n                }),\n                defaultLocale: options.router.defaultLocale,\n                buildId: ''\n            });\n            return Promise.resolve({\n                type: 'redirect-internal',\n                newAs: \"\" + pathname + src.query + src.hash,\n                newUrl: \"\" + pathname + src.query + src.hash\n            });\n        }\n        return Promise.resolve({\n            type: 'redirect-external',\n            destination: redirectTarget\n        });\n    }\n    return Promise.resolve({\n        type: 'next'\n    });\n}\nasync function withMiddlewareEffects(options) {\n    const matches = await matchesMiddleware(options);\n    if (!matches || !options.fetchData) {\n        return null;\n    }\n    const data = await options.fetchData();\n    const effect = await getMiddlewareData(data.dataHref, data.response, options);\n    return {\n        dataHref: data.dataHref,\n        json: data.json,\n        response: data.response,\n        text: data.text,\n        cacheKey: data.cacheKey,\n        effect\n    };\n}\nconst manualScrollRestoration =  false && 0;\nconst SSG_DATA_NOT_FOUND = Symbol('SSG_DATA_NOT_FOUND');\nfunction fetchRetry(url, attempts, options) {\n    return fetch(url, {\n        // Cookies are required to be present for Next.js' SSG \"Preview Mode\".\n        // Cookies may also be required for `getServerSideProps`.\n        //\n        // > `fetch` won’t send cookies, unless you set the credentials init\n        // > option.\n        // https://developer.mozilla.org/docs/Web/API/Fetch_API/Using_Fetch\n        //\n        // > For maximum browser compatibility when it comes to sending &\n        // > receiving cookies, always supply the `credentials: 'same-origin'`\n        // > option instead of relying on the default.\n        // https://github.com/github/fetch#caveats\n        credentials: 'same-origin',\n        method: options.method || 'GET',\n        headers: Object.assign({}, options.headers, {\n            'x-nextjs-data': '1'\n        })\n    }).then((response)=>{\n        return !response.ok && attempts > 1 && response.status >= 500 ? fetchRetry(url, attempts - 1, options) : response;\n    });\n}\nfunction tryToParseAsJSON(text) {\n    try {\n        return JSON.parse(text);\n    } catch (error) {\n        return null;\n    }\n}\nfunction fetchNextData(param) {\n    let { dataHref, inflightCache, isPrefetch, hasMiddleware, isServerRender, parseJSON, persistCache, isBackground, unstable_skipClientCache } = param;\n    const { href: cacheKey } = new URL(dataHref, window.location.href);\n    const getData = (params)=>{\n        var _params_method;\n        return fetchRetry(dataHref, isServerRender ? 3 : 1, {\n            headers: Object.assign({}, isPrefetch ? {\n                purpose: 'prefetch'\n            } : {}, isPrefetch && hasMiddleware ? {\n                'x-middleware-prefetch': '1'\n            } : {}),\n            method: (_params_method = params == null ? void 0 : params.method) != null ? _params_method : 'GET'\n        }).then((response)=>{\n            if (response.ok && (params == null ? void 0 : params.method) === 'HEAD') {\n                return {\n                    dataHref,\n                    response,\n                    text: '',\n                    json: {},\n                    cacheKey\n                };\n            }\n            return response.text().then((text)=>{\n                if (!response.ok) {\n                    /**\n             * When the data response is a redirect because of a middleware\n             * we do not consider it an error. The headers must bring the\n             * mapped location.\n             * TODO: Change the status code in the handler.\n             */ if (hasMiddleware && [\n                        301,\n                        302,\n                        307,\n                        308\n                    ].includes(response.status)) {\n                        return {\n                            dataHref,\n                            response,\n                            text,\n                            json: {},\n                            cacheKey\n                        };\n                    }\n                    if (response.status === 404) {\n                        var _tryToParseAsJSON;\n                        if ((_tryToParseAsJSON = tryToParseAsJSON(text)) == null ? void 0 : _tryToParseAsJSON.notFound) {\n                            return {\n                                dataHref,\n                                json: {\n                                    notFound: SSG_DATA_NOT_FOUND\n                                },\n                                response,\n                                text,\n                                cacheKey\n                            };\n                        }\n                    }\n                    const error = new Error(\"Failed to load static props\");\n                    /**\n             * We should only trigger a server-side transition if this was\n             * caused on a client-side transition. Otherwise, we'd get into\n             * an infinite loop.\n             */ if (!isServerRender) {\n                        (0, _routeloader.markAssetError)(error);\n                    }\n                    throw error;\n                }\n                return {\n                    dataHref,\n                    json: parseJSON ? tryToParseAsJSON(text) : null,\n                    response,\n                    text,\n                    cacheKey\n                };\n            });\n        }).then((data)=>{\n            if (!persistCache || \"development\" !== 'production' || 0) {\n                delete inflightCache[cacheKey];\n            }\n            return data;\n        }).catch((err)=>{\n            if (!unstable_skipClientCache) {\n                delete inflightCache[cacheKey];\n            }\n            if (err.message === 'Failed to fetch' || // firefox\n            err.message === 'NetworkError when attempting to fetch resource.' || // safari\n            err.message === 'Load failed') {\n                (0, _routeloader.markAssetError)(err);\n            }\n            throw err;\n        });\n    };\n    // when skipping client cache we wait to update\n    // inflight cache until successful data response\n    // this allows racing click event with fetching newer data\n    // without blocking navigation when stale data is available\n    if (unstable_skipClientCache && persistCache) {\n        return getData({}).then((data)=>{\n            if (data.response.headers.get('x-middleware-cache') !== 'no-cache') {\n                // only update cache if not marked as no-cache\n                inflightCache[cacheKey] = Promise.resolve(data);\n            }\n            return data;\n        });\n    }\n    if (inflightCache[cacheKey] !== undefined) {\n        return inflightCache[cacheKey];\n    }\n    return inflightCache[cacheKey] = getData(isBackground ? {\n        method: 'HEAD'\n    } : {});\n}\nfunction createKey() {\n    return Math.random().toString(36).slice(2, 10);\n}\nfunction handleHardNavigation(param) {\n    let { url, router } = param;\n    // ensure we don't trigger a hard navigation to the same\n    // URL as this can end up with an infinite refresh\n    if (url === (0, _addbasepath.addBasePath)((0, _addlocale.addLocale)(router.asPath, router.locale))) {\n        throw new Error(\"Invariant: attempted to hard navigate to the same URL \" + url + \" \" + location.href);\n    }\n    window.location.href = url;\n}\nconst getCancelledHandler = (param)=>{\n    let { route, router } = param;\n    let cancelled = false;\n    const cancel = router.clc = ()=>{\n        cancelled = true;\n    };\n    const handleCancelled = ()=>{\n        if (cancelled) {\n            const error = new Error('Abort fetching component for route: \"' + route + '\"');\n            error.cancelled = true;\n            throw error;\n        }\n        if (cancel === router.clc) {\n            router.clc = null;\n        }\n    };\n    return handleCancelled;\n};\nclass Router {\n    reload() {\n        window.location.reload();\n    }\n    /**\n   * Go back in history\n   */ back() {\n        window.history.back();\n    }\n    /**\n   * Go forward in history\n   */ forward() {\n        window.history.forward();\n    }\n    /**\n   * Performs a `pushState` with arguments\n   * @param url of the route\n   * @param as masks `url` for the browser\n   * @param options object you can define `shallow` and other options\n   */ push(url, as, options) {\n        if (options === void 0) options = {};\n        if (false) {}\n        ;\n        ({ url, as } = prepareUrlAs(this, url, as));\n        return this.change('pushState', url, as, options);\n    }\n    /**\n   * Performs a `replaceState` with arguments\n   * @param url of the route\n   * @param as masks `url` for the browser\n   * @param options object you can define `shallow` and other options\n   */ replace(url, as, options) {\n        if (options === void 0) options = {};\n        ;\n        ({ url, as } = prepareUrlAs(this, url, as));\n        return this.change('replaceState', url, as, options);\n    }\n    async _bfl(as, resolvedAs, locale, skipNavigate) {\n        if (true) {\n            if (!this._bfl_s && !this._bfl_d) {\n                const { BloomFilter } = __webpack_require__(/*! ../../lib/bloom-filter */ \"./node_modules/next/dist/shared/lib/bloom-filter.js\");\n                let staticFilterData;\n                let dynamicFilterData;\n                try {\n                    ;\n                    ({ __routerFilterStatic: staticFilterData, __routerFilterDynamic: dynamicFilterData } = await (0, _routeloader.getClientBuildManifest)());\n                } catch (err) {\n                    // failed to load build manifest hard navigate\n                    // to be safe\n                    console.error(err);\n                    if (skipNavigate) {\n                        return true;\n                    }\n                    handleHardNavigation({\n                        url: (0, _addbasepath.addBasePath)((0, _addlocale.addLocale)(as, locale || this.locale, this.defaultLocale)),\n                        router: this\n                    });\n                    return new Promise(()=>{});\n                }\n                const routerFilterSValue = {\"numItems\":21,\"errorRate\":0.0001,\"numBits\":403,\"numHashes\":14,\"bitArray\":[0,0,1,0,1,1,0,1,1,1,0,0,1,1,1,0,1,0,0,1,0,0,1,0,0,1,1,1,1,0,0,1,0,0,0,1,1,0,1,0,0,1,1,0,1,1,1,0,1,1,1,1,0,0,0,0,0,1,0,1,1,1,0,1,1,1,0,1,1,0,1,0,1,1,1,0,1,0,0,1,1,0,1,1,1,1,1,1,1,0,0,1,0,0,0,1,0,0,1,1,1,1,0,0,0,1,1,1,0,0,0,1,1,0,0,1,0,0,0,0,1,0,1,1,1,1,1,1,1,1,0,1,0,0,0,1,1,1,1,1,1,0,1,1,1,1,1,0,1,0,0,0,0,1,0,0,1,1,1,1,0,1,1,1,1,1,0,1,0,0,1,1,1,1,0,0,1,0,0,0,1,0,0,0,1,1,1,0,0,0,1,0,0,0,1,0,0,1,0,1,1,0,0,1,0,0,0,0,1,0,0,0,0,1,1,0,0,1,1,1,1,0,1,0,0,0,1,1,1,0,0,1,0,1,0,0,0,1,0,0,0,1,0,1,1,0,1,0,0,0,1,0,1,0,0,0,1,0,1,0,1,1,0,1,1,0,0,1,0,1,0,0,1,1,0,0,0,0,0,1,1,0,0,1,0,1,0,0,0,0,0,1,0,0,1,1,1,0,1,0,1,1,1,0,1,1,1,0,0,0,0,0,0,1,0,1,0,0,0,1,1,0,0,0,0,1,0,0,1,0,0,0,0,0,1,1,0,0,1,0,1,1,1,0,0,1,0,0,1,1,0,0,1,0,1,1,0,1,0,1,1,1,0,0,0,1,0,1,1,0,1,1,1,1,1,1,1,0,1,0,1,0,1,1,1,0,1,0,1,1,0,1,1,1,1,0,0,0,1,0,0,1,1]};\n                if (!staticFilterData && routerFilterSValue) {\n                    staticFilterData = routerFilterSValue ? routerFilterSValue : undefined;\n                }\n                const routerFilterDValue = {\"numItems\":2,\"errorRate\":0.0001,\"numBits\":39,\"numHashes\":14,\"bitArray\":[0,0,0,0,0,0,0,1,1,0,0,0,1,1,1,1,1,1,0,0,1,1,1,1,0,1,0,0,0,0,0,1,0,1,0,1,0,0,1]};\n                if (!dynamicFilterData && routerFilterDValue) {\n                    dynamicFilterData = routerFilterDValue ? routerFilterDValue : undefined;\n                }\n                if (staticFilterData == null ? void 0 : staticFilterData.numHashes) {\n                    this._bfl_s = new BloomFilter(staticFilterData.numItems, staticFilterData.errorRate);\n                    this._bfl_s.import(staticFilterData);\n                }\n                if (dynamicFilterData == null ? void 0 : dynamicFilterData.numHashes) {\n                    this._bfl_d = new BloomFilter(dynamicFilterData.numItems, dynamicFilterData.errorRate);\n                    this._bfl_d.import(dynamicFilterData);\n                }\n            }\n            let matchesBflStatic = false;\n            let matchesBflDynamic = false;\n            const pathsToCheck = [\n                {\n                    as\n                },\n                {\n                    as: resolvedAs\n                }\n            ];\n            for (const { as: curAs, allowMatchCurrent } of pathsToCheck){\n                if (curAs) {\n                    const asNoSlash = (0, _removetrailingslash.removeTrailingSlash)(new URL(curAs, 'http://n').pathname);\n                    const asNoSlashLocale = (0, _addbasepath.addBasePath)((0, _addlocale.addLocale)(asNoSlash, locale || this.locale));\n                    if (allowMatchCurrent || asNoSlash !== (0, _removetrailingslash.removeTrailingSlash)(new URL(this.asPath, 'http://n').pathname)) {\n                        var _this__bfl_s, _this__bfl_s1;\n                        matchesBflStatic = matchesBflStatic || !!((_this__bfl_s = this._bfl_s) == null ? void 0 : _this__bfl_s.contains(asNoSlash)) || !!((_this__bfl_s1 = this._bfl_s) == null ? void 0 : _this__bfl_s1.contains(asNoSlashLocale));\n                        for (const normalizedAS of [\n                            asNoSlash,\n                            asNoSlashLocale\n                        ]){\n                            // if any sub-path of as matches a dynamic filter path\n                            // it should be hard navigated\n                            const curAsParts = normalizedAS.split('/');\n                            for(let i = 0; !matchesBflDynamic && i < curAsParts.length + 1; i++){\n                                var _this__bfl_d;\n                                const currentPart = curAsParts.slice(0, i).join('/');\n                                if (currentPart && ((_this__bfl_d = this._bfl_d) == null ? void 0 : _this__bfl_d.contains(currentPart))) {\n                                    matchesBflDynamic = true;\n                                    break;\n                                }\n                            }\n                        }\n                        // if the client router filter is matched then we trigger\n                        // a hard navigation\n                        if (matchesBflStatic || matchesBflDynamic) {\n                            if (skipNavigate) {\n                                return true;\n                            }\n                            handleHardNavigation({\n                                url: (0, _addbasepath.addBasePath)((0, _addlocale.addLocale)(as, locale || this.locale, this.defaultLocale)),\n                                router: this\n                            });\n                            return new Promise(()=>{});\n                        }\n                    }\n                }\n            }\n        }\n        return false;\n    }\n    async change(method, url, as, options, forcedScroll) {\n        var _this_components_pathname;\n        if (!(0, _islocalurl.isLocalURL)(url)) {\n            handleHardNavigation({\n                url,\n                router: this\n            });\n            return false;\n        }\n        // WARNING: `_h` is an internal option for handing Next.js client-side\n        // hydration. Your app should _never_ use this property. It may change at\n        // any time without notice.\n        const isQueryUpdating = options._h === 1;\n        if (!isQueryUpdating && !options.shallow) {\n            await this._bfl(as, undefined, options.locale);\n        }\n        let shouldResolveHref = isQueryUpdating || options._shouldResolveHref || (0, _parsepath.parsePath)(url).pathname === (0, _parsepath.parsePath)(as).pathname;\n        const nextState = {\n            ...this.state\n        };\n        // for static pages with query params in the URL we delay\n        // marking the router ready until after the query is updated\n        // or a navigation has occurred\n        const readyStateChange = this.isReady !== true;\n        this.isReady = true;\n        const isSsr = this.isSsr;\n        if (!isQueryUpdating) {\n            this.isSsr = false;\n        }\n        // if a route transition is already in progress before\n        // the query updating is triggered ignore query updating\n        if (isQueryUpdating && this.clc) {\n            return false;\n        }\n        const prevLocale = nextState.locale;\n        if (false) { var _this_locales; }\n        // marking route changes as a navigation start entry\n        if (_utils.ST) {\n            performance.mark('routeChange');\n        }\n        const { shallow = false, scroll = true } = options;\n        const routeProps = {\n            shallow\n        };\n        if (this._inFlightRoute && this.clc) {\n            if (!isSsr) {\n                Router.events.emit('routeChangeError', buildCancellationError(), this._inFlightRoute, routeProps);\n            }\n            this.clc();\n            this.clc = null;\n        }\n        as = (0, _addbasepath.addBasePath)((0, _addlocale.addLocale)((0, _hasbasepath.hasBasePath)(as) ? (0, _removebasepath.removeBasePath)(as) : as, options.locale, this.defaultLocale));\n        const cleanedAs = (0, _removelocale.removeLocale)((0, _hasbasepath.hasBasePath)(as) ? (0, _removebasepath.removeBasePath)(as) : as, nextState.locale);\n        this._inFlightRoute = as;\n        const localeChange = prevLocale !== nextState.locale;\n        // If the url change is only related to a hash change\n        // We should not proceed. We should only change the state.\n        if (!isQueryUpdating && this.onlyAHashChange(cleanedAs) && !localeChange) {\n            nextState.asPath = cleanedAs;\n            Router.events.emit('hashChangeStart', as, routeProps);\n            // TODO: do we need the resolved href when only a hash change?\n            this.changeState(method, url, as, {\n                ...options,\n                scroll: false\n            });\n            if (scroll) {\n                this.scrollToHash(cleanedAs);\n            }\n            try {\n                await this.set(nextState, this.components[nextState.route], null);\n            } catch (err) {\n                if ((0, _iserror.default)(err) && err.cancelled) {\n                    Router.events.emit('routeChangeError', err, cleanedAs, routeProps);\n                }\n                throw err;\n            }\n            Router.events.emit('hashChangeComplete', as, routeProps);\n            return true;\n        }\n        let parsed = (0, _parserelativeurl.parseRelativeUrl)(url);\n        let { pathname, query } = parsed;\n        // The build manifest needs to be loaded before auto-static dynamic pages\n        // get their query parameters to allow ensuring they can be parsed properly\n        // when rewritten to\n        let pages, rewrites;\n        try {\n            ;\n            [pages, { __rewrites: rewrites }] = await Promise.all([\n                this.pageLoader.getPageList(),\n                (0, _routeloader.getClientBuildManifest)(),\n                this.pageLoader.getMiddleware()\n            ]);\n        } catch (err) {\n            // If we fail to resolve the page list or client-build manifest, we must\n            // do a server-side transition:\n            handleHardNavigation({\n                url: as,\n                router: this\n            });\n            return false;\n        }\n        // If asked to change the current URL we should reload the current page\n        // (not location.reload() but reload getInitialProps and other Next.js stuffs)\n        // We also need to set the method = replaceState always\n        // as this should not go into the history (That's how browsers work)\n        // We should compare the new asPath to the current asPath, not the url\n        if (!this.urlIsNew(cleanedAs) && !localeChange) {\n            method = 'replaceState';\n        }\n        // we need to resolve the as value using rewrites for dynamic SSG\n        // pages to allow building the data URL correctly\n        let resolvedAs = as;\n        // url and as should always be prefixed with basePath by this\n        // point by either next/link or router.push/replace so strip the\n        // basePath from the pathname to match the pages dir 1-to-1\n        pathname = pathname ? (0, _removetrailingslash.removeTrailingSlash)((0, _removebasepath.removeBasePath)(pathname)) : pathname;\n        let route = (0, _removetrailingslash.removeTrailingSlash)(pathname);\n        const parsedAsPathname = as.startsWith('/') && (0, _parserelativeurl.parseRelativeUrl)(as).pathname;\n        // if we detected the path as app route during prefetching\n        // trigger hard navigation\n        if ((_this_components_pathname = this.components[pathname]) == null ? void 0 : _this_components_pathname.__appRouter) {\n            handleHardNavigation({\n                url: as,\n                router: this\n            });\n            return new Promise(()=>{});\n        }\n        const isMiddlewareRewrite = !!(parsedAsPathname && route !== parsedAsPathname && (!(0, _isdynamic.isDynamicRoute)(route) || !(0, _routematcher.getRouteMatcher)((0, _routeregex.getRouteRegex)(route))(parsedAsPathname)));\n        // we don't attempt resolve asPath when we need to execute\n        // middleware as the resolving will occur server-side\n        const isMiddlewareMatch = !options.shallow && await matchesMiddleware({\n            asPath: as,\n            locale: nextState.locale,\n            router: this\n        });\n        if (isQueryUpdating && isMiddlewareMatch) {\n            shouldResolveHref = false;\n        }\n        if (shouldResolveHref && pathname !== '/_error') {\n            ;\n            options._shouldResolveHref = true;\n            if (false) {} else {\n                parsed.pathname = resolveDynamicRoute(pathname, pages);\n                if (parsed.pathname !== pathname) {\n                    pathname = parsed.pathname;\n                    parsed.pathname = (0, _addbasepath.addBasePath)(pathname);\n                    if (!isMiddlewareMatch) {\n                        url = (0, _formaturl.formatWithValidation)(parsed);\n                    }\n                }\n            }\n        }\n        if (!(0, _islocalurl.isLocalURL)(as)) {\n            if (true) {\n                throw new Error('Invalid href: \"' + url + '\" and as: \"' + as + '\", received relative href and external as' + \"\\nSee more info: https://nextjs.org/docs/messages/invalid-relative-url-external-as\");\n            }\n            handleHardNavigation({\n                url: as,\n                router: this\n            });\n            return false;\n        }\n        resolvedAs = (0, _removelocale.removeLocale)((0, _removebasepath.removeBasePath)(resolvedAs), nextState.locale);\n        route = (0, _removetrailingslash.removeTrailingSlash)(pathname);\n        let routeMatch = false;\n        if ((0, _isdynamic.isDynamicRoute)(route)) {\n            const parsedAs = (0, _parserelativeurl.parseRelativeUrl)(resolvedAs);\n            const asPathname = parsedAs.pathname;\n            const routeRegex = (0, _routeregex.getRouteRegex)(route);\n            routeMatch = (0, _routematcher.getRouteMatcher)(routeRegex)(asPathname);\n            const shouldInterpolate = route === asPathname;\n            const interpolatedAs = shouldInterpolate ? (0, _interpolateas.interpolateAs)(route, asPathname, query) : {};\n            if (!routeMatch || shouldInterpolate && !interpolatedAs.result) {\n                const missingParams = Object.keys(routeRegex.groups).filter((param)=>!query[param] && !routeRegex.groups[param].optional);\n                if (missingParams.length > 0 && !isMiddlewareMatch) {\n                    if (true) {\n                        console.warn(\"\" + (shouldInterpolate ? \"Interpolating href\" : \"Mismatching `as` and `href`\") + \" failed to manually provide \" + (\"the params: \" + missingParams.join(', ') + \" in the `href`'s `query`\"));\n                    }\n                    throw new Error((shouldInterpolate ? \"The provided `href` (\" + url + \") value is missing query values (\" + missingParams.join(', ') + \") to be interpolated properly. \" : \"The provided `as` value (\" + asPathname + \") is incompatible with the `href` value (\" + route + \"). \") + (\"Read more: https://nextjs.org/docs/messages/\" + (shouldInterpolate ? 'href-interpolation-failed' : 'incompatible-href-as')));\n                }\n            } else if (shouldInterpolate) {\n                as = (0, _formaturl.formatWithValidation)(Object.assign({}, parsedAs, {\n                    pathname: interpolatedAs.result,\n                    query: (0, _omit.omit)(query, interpolatedAs.params)\n                }));\n            } else {\n                // Merge params into `query`, overwriting any specified in search\n                Object.assign(query, routeMatch);\n            }\n        }\n        if (!isQueryUpdating) {\n            Router.events.emit('routeChangeStart', as, routeProps);\n        }\n        const isErrorRoute = this.pathname === '/404' || this.pathname === '/_error';\n        try {\n            var _self___NEXT_DATA___props_pageProps, _self___NEXT_DATA___props, _routeInfo_props;\n            let routeInfo = await this.getRouteInfo({\n                route,\n                pathname,\n                query,\n                as,\n                resolvedAs,\n                routeProps,\n                locale: nextState.locale,\n                isPreview: nextState.isPreview,\n                hasMiddleware: isMiddlewareMatch,\n                unstable_skipClientCache: options.unstable_skipClientCache,\n                isQueryUpdating: isQueryUpdating && !this.isFallback,\n                isMiddlewareRewrite\n            });\n            if (!isQueryUpdating && !options.shallow) {\n                await this._bfl(as, 'resolvedAs' in routeInfo ? routeInfo.resolvedAs : undefined, nextState.locale);\n            }\n            if ('route' in routeInfo && isMiddlewareMatch) {\n                pathname = routeInfo.route || route;\n                route = pathname;\n                if (!routeProps.shallow) {\n                    query = Object.assign({}, routeInfo.query || {}, query);\n                }\n                const cleanedParsedPathname = (0, _hasbasepath.hasBasePath)(parsed.pathname) ? (0, _removebasepath.removeBasePath)(parsed.pathname) : parsed.pathname;\n                if (routeMatch && pathname !== cleanedParsedPathname) {\n                    Object.keys(routeMatch).forEach((key)=>{\n                        if (routeMatch && query[key] === routeMatch[key]) {\n                            delete query[key];\n                        }\n                    });\n                }\n                if ((0, _isdynamic.isDynamicRoute)(pathname)) {\n                    const prefixedAs = !routeProps.shallow && routeInfo.resolvedAs ? routeInfo.resolvedAs : (0, _addbasepath.addBasePath)((0, _addlocale.addLocale)(new URL(as, location.href).pathname, nextState.locale), true);\n                    let rewriteAs = prefixedAs;\n                    if ((0, _hasbasepath.hasBasePath)(rewriteAs)) {\n                        rewriteAs = (0, _removebasepath.removeBasePath)(rewriteAs);\n                    }\n                    if (false) {}\n                    const routeRegex = (0, _routeregex.getRouteRegex)(pathname);\n                    const curRouteMatch = (0, _routematcher.getRouteMatcher)(routeRegex)(new URL(rewriteAs, location.href).pathname);\n                    if (curRouteMatch) {\n                        Object.assign(query, curRouteMatch);\n                    }\n                }\n            }\n            // If the routeInfo brings a redirect we simply apply it.\n            if ('type' in routeInfo) {\n                if (routeInfo.type === 'redirect-internal') {\n                    return this.change(method, routeInfo.newUrl, routeInfo.newAs, options);\n                } else {\n                    handleHardNavigation({\n                        url: routeInfo.destination,\n                        router: this\n                    });\n                    return new Promise(()=>{});\n                }\n            }\n            const component = routeInfo.Component;\n            if (component && component.unstable_scriptLoader) {\n                const scripts = [].concat(component.unstable_scriptLoader());\n                scripts.forEach((script)=>{\n                    (0, _script.handleClientScriptLoad)(script.props);\n                });\n            }\n            // handle redirect on client-transition\n            if ((routeInfo.__N_SSG || routeInfo.__N_SSP) && routeInfo.props) {\n                if (routeInfo.props.pageProps && routeInfo.props.pageProps.__N_REDIRECT) {\n                    // Use the destination from redirect without adding locale\n                    options.locale = false;\n                    const destination = routeInfo.props.pageProps.__N_REDIRECT;\n                    // check if destination is internal (resolves to a page) and attempt\n                    // client-navigation if it is falling back to hard navigation if\n                    // it's not\n                    if (destination.startsWith('/') && routeInfo.props.pageProps.__N_REDIRECT_BASE_PATH !== false) {\n                        const parsedHref = (0, _parserelativeurl.parseRelativeUrl)(destination);\n                        parsedHref.pathname = resolveDynamicRoute(parsedHref.pathname, pages);\n                        const { url: newUrl, as: newAs } = prepareUrlAs(this, destination, destination);\n                        return this.change(method, newUrl, newAs, options);\n                    }\n                    handleHardNavigation({\n                        url: destination,\n                        router: this\n                    });\n                    return new Promise(()=>{});\n                }\n                nextState.isPreview = !!routeInfo.props.__N_PREVIEW;\n                // handle SSG data 404\n                if (routeInfo.props.notFound === SSG_DATA_NOT_FOUND) {\n                    let notFoundRoute;\n                    try {\n                        await this.fetchComponent('/404');\n                        notFoundRoute = '/404';\n                    } catch (_) {\n                        notFoundRoute = '/_error';\n                    }\n                    routeInfo = await this.getRouteInfo({\n                        route: notFoundRoute,\n                        pathname: notFoundRoute,\n                        query,\n                        as,\n                        resolvedAs,\n                        routeProps: {\n                            shallow: false\n                        },\n                        locale: nextState.locale,\n                        isPreview: nextState.isPreview,\n                        isNotFound: true\n                    });\n                    if ('type' in routeInfo) {\n                        throw new Error(\"Unexpected middleware effect on /404\");\n                    }\n                }\n            }\n            if (isQueryUpdating && this.pathname === '/_error' && ((_self___NEXT_DATA___props = self.__NEXT_DATA__.props) == null ? void 0 : (_self___NEXT_DATA___props_pageProps = _self___NEXT_DATA___props.pageProps) == null ? void 0 : _self___NEXT_DATA___props_pageProps.statusCode) === 500 && ((_routeInfo_props = routeInfo.props) == null ? void 0 : _routeInfo_props.pageProps)) {\n                // ensure statusCode is still correct for static 500 page\n                // when updating query information\n                routeInfo.props.pageProps.statusCode = 500;\n            }\n            var _routeInfo_route;\n            // shallow routing is only allowed for same page URL changes.\n            const isValidShallowRoute = options.shallow && nextState.route === ((_routeInfo_route = routeInfo.route) != null ? _routeInfo_route : route);\n            var _options_scroll;\n            const shouldScroll = (_options_scroll = options.scroll) != null ? _options_scroll : !isQueryUpdating && !isValidShallowRoute;\n            const resetScroll = shouldScroll ? {\n                x: 0,\n                y: 0\n            } : null;\n            const upcomingScrollState = forcedScroll != null ? forcedScroll : resetScroll;\n            // the new state that the router gonna set\n            const upcomingRouterState = {\n                ...nextState,\n                route,\n                pathname,\n                query,\n                asPath: cleanedAs,\n                isFallback: false\n            };\n            // When the page being rendered is the 404 page, we should only update the\n            // query parameters. Route changes here might add the basePath when it\n            // wasn't originally present. This is also why this block is before the\n            // below `changeState` call which updates the browser's history (changing\n            // the URL).\n            if (isQueryUpdating && isErrorRoute) {\n                var _self___NEXT_DATA___props_pageProps1, _self___NEXT_DATA___props1, _routeInfo_props1;\n                routeInfo = await this.getRouteInfo({\n                    route: this.pathname,\n                    pathname: this.pathname,\n                    query,\n                    as,\n                    resolvedAs,\n                    routeProps: {\n                        shallow: false\n                    },\n                    locale: nextState.locale,\n                    isPreview: nextState.isPreview,\n                    isQueryUpdating: isQueryUpdating && !this.isFallback\n                });\n                if ('type' in routeInfo) {\n                    throw new Error(\"Unexpected middleware effect on \" + this.pathname);\n                }\n                if (this.pathname === '/_error' && ((_self___NEXT_DATA___props1 = self.__NEXT_DATA__.props) == null ? void 0 : (_self___NEXT_DATA___props_pageProps1 = _self___NEXT_DATA___props1.pageProps) == null ? void 0 : _self___NEXT_DATA___props_pageProps1.statusCode) === 500 && ((_routeInfo_props1 = routeInfo.props) == null ? void 0 : _routeInfo_props1.pageProps)) {\n                    // ensure statusCode is still correct for static 500 page\n                    // when updating query information\n                    routeInfo.props.pageProps.statusCode = 500;\n                }\n                try {\n                    await this.set(upcomingRouterState, routeInfo, upcomingScrollState);\n                } catch (err) {\n                    if ((0, _iserror.default)(err) && err.cancelled) {\n                        Router.events.emit('routeChangeError', err, cleanedAs, routeProps);\n                    }\n                    throw err;\n                }\n                return true;\n            }\n            Router.events.emit('beforeHistoryChange', as, routeProps);\n            this.changeState(method, url, as, options);\n            // for query updates we can skip it if the state is unchanged and we don't\n            // need to scroll\n            // https://github.com/vercel/next.js/issues/37139\n            const canSkipUpdating = isQueryUpdating && !upcomingScrollState && !readyStateChange && !localeChange && (0, _comparestates.compareRouterStates)(upcomingRouterState, this.state);\n            if (!canSkipUpdating) {\n                try {\n                    await this.set(upcomingRouterState, routeInfo, upcomingScrollState);\n                } catch (e) {\n                    if (e.cancelled) routeInfo.error = routeInfo.error || e;\n                    else throw e;\n                }\n                if (routeInfo.error) {\n                    if (!isQueryUpdating) {\n                        Router.events.emit('routeChangeError', routeInfo.error, cleanedAs, routeProps);\n                    }\n                    throw routeInfo.error;\n                }\n                if (false) {}\n                if (!isQueryUpdating) {\n                    Router.events.emit('routeChangeComplete', as, routeProps);\n                }\n                // A hash mark # is the optional last part of a URL\n                const hashRegex = /#.+$/;\n                if (shouldScroll && hashRegex.test(as)) {\n                    this.scrollToHash(as);\n                }\n            }\n            return true;\n        } catch (err) {\n            if ((0, _iserror.default)(err) && err.cancelled) {\n                return false;\n            }\n            throw err;\n        }\n    }\n    changeState(method, url, as, options) {\n        if (options === void 0) options = {};\n        if (true) {\n            if (typeof window.history === 'undefined') {\n                console.error(\"Warning: window.history is not available.\");\n                return;\n            }\n            if (typeof window.history[method] === 'undefined') {\n                console.error(\"Warning: window.history.\" + method + \" is not available\");\n                return;\n            }\n        }\n        if (method !== 'pushState' || (0, _utils.getURL)() !== as) {\n            this._shallow = options.shallow;\n            window.history[method]({\n                url,\n                as,\n                options,\n                __N: true,\n                key: this._key = method !== 'pushState' ? this._key : createKey()\n            }, // Passing the empty string here should be safe against future changes to the method.\n            // https://developer.mozilla.org/docs/Web/API/History/replaceState\n            '', as);\n        }\n    }\n    async handleRouteInfoError(err, pathname, query, as, routeProps, loadErrorFail) {\n        if (err.cancelled) {\n            // bubble up cancellation errors\n            throw err;\n        }\n        if ((0, _routeloader.isAssetError)(err) || loadErrorFail) {\n            Router.events.emit('routeChangeError', err, as, routeProps);\n            // If we can't load the page it could be one of following reasons\n            //  1. Page doesn't exists\n            //  2. Page does exist in a different zone\n            //  3. Internal error while loading the page\n            // So, doing a hard reload is the proper way to deal with this.\n            handleHardNavigation({\n                url: as,\n                router: this\n            });\n            // Changing the URL doesn't block executing the current code path.\n            // So let's throw a cancellation error stop the routing logic.\n            throw buildCancellationError();\n        }\n        console.error(err);\n        try {\n            let props;\n            const { page: Component, styleSheets } = await this.fetchComponent('/_error');\n            const routeInfo = {\n                props,\n                Component,\n                styleSheets,\n                err,\n                error: err\n            };\n            if (!routeInfo.props) {\n                try {\n                    routeInfo.props = await this.getInitialProps(Component, {\n                        err,\n                        pathname,\n                        query\n                    });\n                } catch (gipErr) {\n                    console.error('Error in error page `getInitialProps`: ', gipErr);\n                    routeInfo.props = {};\n                }\n            }\n            return routeInfo;\n        } catch (routeInfoErr) {\n            return this.handleRouteInfoError((0, _iserror.default)(routeInfoErr) ? routeInfoErr : new Error(routeInfoErr + ''), pathname, query, as, routeProps, true);\n        }\n    }\n    async getRouteInfo(param) {\n        let { route: requestedRoute, pathname, query, as, resolvedAs, routeProps, locale, hasMiddleware, isPreview, unstable_skipClientCache, isQueryUpdating, isMiddlewareRewrite, isNotFound } = param;\n        /**\n     * This `route` binding can change if there's a rewrite\n     * so we keep a reference to the original requested route\n     * so we can store the cache for it and avoid re-requesting every time\n     * for shallow routing purposes.\n     */ let route = requestedRoute;\n        try {\n            var _data_effect, _data_effect1, _data_effect2, _data_response;\n            let existingInfo = this.components[route];\n            if (routeProps.shallow && existingInfo && this.route === route) {\n                return existingInfo;\n            }\n            const handleCancelled = getCancelledHandler({\n                route,\n                router: this\n            });\n            if (hasMiddleware) {\n                existingInfo = undefined;\n            }\n            let cachedRouteInfo = existingInfo && !('initial' in existingInfo) && \"development\" !== 'development' ? 0 : undefined;\n            const isBackground = isQueryUpdating;\n            const fetchNextDataParams = {\n                dataHref: this.pageLoader.getDataHref({\n                    href: (0, _formaturl.formatWithValidation)({\n                        pathname,\n                        query\n                    }),\n                    skipInterpolation: true,\n                    asPath: isNotFound ? '/404' : resolvedAs,\n                    locale\n                }),\n                hasMiddleware: true,\n                isServerRender: this.isSsr,\n                parseJSON: true,\n                inflightCache: isBackground ? this.sbc : this.sdc,\n                persistCache: !isPreview,\n                isPrefetch: false,\n                unstable_skipClientCache,\n                isBackground\n            };\n            let data = isQueryUpdating && !isMiddlewareRewrite ? null : await withMiddlewareEffects({\n                fetchData: ()=>fetchNextData(fetchNextDataParams),\n                asPath: isNotFound ? '/404' : resolvedAs,\n                locale: locale,\n                router: this\n            }).catch((err)=>{\n                // we don't hard error during query updating\n                // as it's un-necessary and doesn't need to be fatal\n                // unless it is a fallback route and the props can't\n                // be loaded\n                if (isQueryUpdating) {\n                    return null;\n                }\n                throw err;\n            });\n            // when rendering error routes we don't apply middleware\n            // effects\n            if (data && (pathname === '/_error' || pathname === '/404')) {\n                data.effect = undefined;\n            }\n            if (isQueryUpdating) {\n                if (!data) {\n                    data = {\n                        json: self.__NEXT_DATA__.props\n                    };\n                } else {\n                    data.json = self.__NEXT_DATA__.props;\n                }\n            }\n            handleCancelled();\n            if ((data == null ? void 0 : (_data_effect = data.effect) == null ? void 0 : _data_effect.type) === 'redirect-internal' || (data == null ? void 0 : (_data_effect1 = data.effect) == null ? void 0 : _data_effect1.type) === 'redirect-external') {\n                return data.effect;\n            }\n            if ((data == null ? void 0 : (_data_effect2 = data.effect) == null ? void 0 : _data_effect2.type) === 'rewrite') {\n                const resolvedRoute = (0, _removetrailingslash.removeTrailingSlash)(data.effect.resolvedHref);\n                const pages = await this.pageLoader.getPageList();\n                // during query updating the page must match although during\n                // client-transition a redirect that doesn't match a page\n                // can be returned and this should trigger a hard navigation\n                // which is valid for incremental migration\n                if (!isQueryUpdating || pages.includes(resolvedRoute)) {\n                    route = resolvedRoute;\n                    pathname = data.effect.resolvedHref;\n                    query = {\n                        ...query,\n                        ...data.effect.parsedAs.query\n                    };\n                    resolvedAs = (0, _removebasepath.removeBasePath)((0, _normalizelocalepath.normalizeLocalePath)(data.effect.parsedAs.pathname, this.locales).pathname);\n                    // Check again the cache with the new destination.\n                    existingInfo = this.components[route];\n                    if (routeProps.shallow && existingInfo && this.route === route && !hasMiddleware) {\n                        // If we have a match with the current route due to rewrite,\n                        // we can copy the existing information to the rewritten one.\n                        // Then, we return the information along with the matched route.\n                        return {\n                            ...existingInfo,\n                            route\n                        };\n                    }\n                }\n            }\n            if ((0, _isapiroute.isAPIRoute)(route)) {\n                handleHardNavigation({\n                    url: as,\n                    router: this\n                });\n                return new Promise(()=>{});\n            }\n            const routeInfo = cachedRouteInfo || await this.fetchComponent(route).then((res)=>({\n                    Component: res.page,\n                    styleSheets: res.styleSheets,\n                    __N_SSG: res.mod.__N_SSG,\n                    __N_SSP: res.mod.__N_SSP\n                }));\n            if (true) {\n                const { isValidElementType } = __webpack_require__(/*! next/dist/compiled/react-is */ \"./node_modules/next/dist/compiled/react-is/index.js\");\n                if (!isValidElementType(routeInfo.Component)) {\n                    throw new Error('The default export is not a React Component in page: \"' + pathname + '\"');\n                }\n            }\n            const wasBailedPrefetch = data == null ? void 0 : (_data_response = data.response) == null ? void 0 : _data_response.headers.get('x-middleware-skip');\n            const shouldFetchData = routeInfo.__N_SSG || routeInfo.__N_SSP;\n            // For non-SSG prefetches that bailed before sending data\n            // we clear the cache to fetch full response\n            if (wasBailedPrefetch && (data == null ? void 0 : data.dataHref)) {\n                delete this.sdc[data.dataHref];\n            }\n            const { props, cacheKey } = await this._getData(async ()=>{\n                if (shouldFetchData) {\n                    if ((data == null ? void 0 : data.json) && !wasBailedPrefetch) {\n                        return {\n                            cacheKey: data.cacheKey,\n                            props: data.json\n                        };\n                    }\n                    const dataHref = (data == null ? void 0 : data.dataHref) ? data.dataHref : this.pageLoader.getDataHref({\n                        href: (0, _formaturl.formatWithValidation)({\n                            pathname,\n                            query\n                        }),\n                        asPath: resolvedAs,\n                        locale\n                    });\n                    const fetched = await fetchNextData({\n                        dataHref,\n                        isServerRender: this.isSsr,\n                        parseJSON: true,\n                        inflightCache: wasBailedPrefetch ? {} : this.sdc,\n                        persistCache: !isPreview,\n                        isPrefetch: false,\n                        unstable_skipClientCache\n                    });\n                    return {\n                        cacheKey: fetched.cacheKey,\n                        props: fetched.json || {}\n                    };\n                }\n                return {\n                    headers: {},\n                    props: await this.getInitialProps(routeInfo.Component, {\n                        pathname,\n                        query,\n                        asPath: as,\n                        locale,\n                        locales: this.locales,\n                        defaultLocale: this.defaultLocale\n                    })\n                };\n            });\n            // Only bust the data cache for SSP routes although\n            // middleware can skip cache per request with\n            // x-middleware-cache: no-cache as well\n            if (routeInfo.__N_SSP && fetchNextDataParams.dataHref && cacheKey) {\n                delete this.sdc[cacheKey];\n            }\n            // we kick off a HEAD request in the background\n            // when a non-prefetch request is made to signal revalidation\n            if (!this.isPreview && routeInfo.__N_SSG && \"development\" !== 'development' && 0) {}\n            props.pageProps = Object.assign({}, props.pageProps);\n            routeInfo.props = props;\n            routeInfo.route = route;\n            routeInfo.query = query;\n            routeInfo.resolvedAs = resolvedAs;\n            this.components[route] = routeInfo;\n            return routeInfo;\n        } catch (err) {\n            return this.handleRouteInfoError((0, _iserror.getProperError)(err), pathname, query, as, routeProps);\n        }\n    }\n    set(state, data, resetScroll) {\n        this.state = state;\n        return this.sub(data, this.components['/_app'].Component, resetScroll);\n    }\n    /**\n   * Callback to execute before replacing router state\n   * @param cb callback to be executed\n   */ beforePopState(cb) {\n        this._bps = cb;\n    }\n    onlyAHashChange(as) {\n        if (!this.asPath) return false;\n        const [oldUrlNoHash, oldHash] = this.asPath.split('#', 2);\n        const [newUrlNoHash, newHash] = as.split('#', 2);\n        // Makes sure we scroll to the provided hash if the url/hash are the same\n        if (newHash && oldUrlNoHash === newUrlNoHash && oldHash === newHash) {\n            return true;\n        }\n        // If the urls are change, there's more than a hash change\n        if (oldUrlNoHash !== newUrlNoHash) {\n            return false;\n        }\n        // If the hash has changed, then it's a hash only change.\n        // This check is necessary to handle both the enter and\n        // leave hash === '' cases. The identity case falls through\n        // and is treated as a next reload.\n        return oldHash !== newHash;\n    }\n    scrollToHash(as) {\n        const [, hash = ''] = as.split('#', 2);\n        (0, _handlesmoothscroll.handleSmoothScroll)(()=>{\n            // Scroll to top if the hash is just `#` with no value or `#top`\n            // To mirror browsers\n            if (hash === '' || hash === 'top') {\n                window.scrollTo(0, 0);\n                return;\n            }\n            // Decode hash to make non-latin anchor works.\n            const rawHash = decodeURIComponent(hash);\n            // First we check if the element by id is found\n            const idEl = document.getElementById(rawHash);\n            if (idEl) {\n                idEl.scrollIntoView();\n                return;\n            }\n            // If there's no element with the id, we check the `name` property\n            // To mirror browsers\n            const nameEl = document.getElementsByName(rawHash)[0];\n            if (nameEl) {\n                nameEl.scrollIntoView();\n            }\n        }, {\n            onlyHashChange: this.onlyAHashChange(as)\n        });\n    }\n    urlIsNew(asPath) {\n        return this.asPath !== asPath;\n    }\n    /**\n   * Prefetch page code, you may wait for the data during page rendering.\n   * This feature only works in production!\n   * @param url the href of prefetched page\n   * @param asPath the as path of the prefetched page\n   */ async prefetch(url, asPath, options) {\n        if (asPath === void 0) asPath = url;\n        if (options === void 0) options = {};\n        // Prefetch is not supported in development mode because it would trigger on-demand-entries\n        if (true) {\n            return;\n        }\n        if ( true && (0, _isbot.isBot)(window.navigator.userAgent)) {\n            // No prefetches for bots that render the link since they are typically navigating\n            // links via the equivalent of a hard navigation and hence never utilize these\n            // prefetches.\n            return;\n        }\n        let parsed = (0, _parserelativeurl.parseRelativeUrl)(url);\n        const urlPathname = parsed.pathname;\n        let { pathname, query } = parsed;\n        const originalPathname = pathname;\n        if (false) {}\n        const pages = await this.pageLoader.getPageList();\n        let resolvedAs = asPath;\n        const locale = typeof options.locale !== 'undefined' ? options.locale || undefined : this.locale;\n        const isMiddlewareMatch = await matchesMiddleware({\n            asPath: asPath,\n            locale: locale,\n            router: this\n        });\n        if (false) {}\n        parsed.pathname = resolveDynamicRoute(parsed.pathname, pages);\n        if ((0, _isdynamic.isDynamicRoute)(parsed.pathname)) {\n            pathname = parsed.pathname;\n            parsed.pathname = pathname;\n            Object.assign(query, (0, _routematcher.getRouteMatcher)((0, _routeregex.getRouteRegex)(parsed.pathname))((0, _parsepath.parsePath)(asPath).pathname) || {});\n            if (!isMiddlewareMatch) {\n                url = (0, _formaturl.formatWithValidation)(parsed);\n            }\n        }\n        const data =  false ? 0 : await withMiddlewareEffects({\n            fetchData: ()=>fetchNextData({\n                    dataHref: this.pageLoader.getDataHref({\n                        href: (0, _formaturl.formatWithValidation)({\n                            pathname: originalPathname,\n                            query\n                        }),\n                        skipInterpolation: true,\n                        asPath: resolvedAs,\n                        locale\n                    }),\n                    hasMiddleware: true,\n                    isServerRender: false,\n                    parseJSON: true,\n                    inflightCache: this.sdc,\n                    persistCache: !this.isPreview,\n                    isPrefetch: true\n                }),\n            asPath: asPath,\n            locale: locale,\n            router: this\n        });\n        /**\n     * If there was a rewrite we apply the effects of the rewrite on the\n     * current parameters for the prefetch.\n     */ if ((data == null ? void 0 : data.effect.type) === 'rewrite') {\n            parsed.pathname = data.effect.resolvedHref;\n            pathname = data.effect.resolvedHref;\n            query = {\n                ...query,\n                ...data.effect.parsedAs.query\n            };\n            resolvedAs = data.effect.parsedAs.pathname;\n            url = (0, _formaturl.formatWithValidation)(parsed);\n        }\n        /**\n     * If there is a redirect to an external destination then we don't have\n     * to prefetch content as it will be unused.\n     */ if ((data == null ? void 0 : data.effect.type) === 'redirect-external') {\n            return;\n        }\n        const route = (0, _removetrailingslash.removeTrailingSlash)(pathname);\n        if (await this._bfl(asPath, resolvedAs, options.locale, true)) {\n            this.components[urlPathname] = {\n                __appRouter: true\n            };\n        }\n        await Promise.all([\n            this.pageLoader._isSsg(route).then((isSsg)=>{\n                return isSsg ? fetchNextData({\n                    dataHref: (data == null ? void 0 : data.json) ? data == null ? void 0 : data.dataHref : this.pageLoader.getDataHref({\n                        href: url,\n                        asPath: resolvedAs,\n                        locale: locale\n                    }),\n                    isServerRender: false,\n                    parseJSON: true,\n                    inflightCache: this.sdc,\n                    persistCache: !this.isPreview,\n                    isPrefetch: true,\n                    unstable_skipClientCache: options.unstable_skipClientCache || options.priority && !!true\n                }).then(()=>false).catch(()=>false) : false;\n            }),\n            this.pageLoader[options.priority ? 'loadPage' : 'prefetch'](route)\n        ]);\n    }\n    async fetchComponent(route) {\n        const handleCancelled = getCancelledHandler({\n            route,\n            router: this\n        });\n        try {\n            const componentResult = await this.pageLoader.loadPage(route);\n            handleCancelled();\n            return componentResult;\n        } catch (err) {\n            handleCancelled();\n            throw err;\n        }\n    }\n    _getData(fn) {\n        let cancelled = false;\n        const cancel = ()=>{\n            cancelled = true;\n        };\n        this.clc = cancel;\n        return fn().then((data)=>{\n            if (cancel === this.clc) {\n                this.clc = null;\n            }\n            if (cancelled) {\n                const err = new Error('Loading initial props cancelled');\n                err.cancelled = true;\n                throw err;\n            }\n            return data;\n        });\n    }\n    getInitialProps(Component, ctx) {\n        const { Component: App } = this.components['/_app'];\n        const AppTree = this._wrapApp(App);\n        ctx.AppTree = AppTree;\n        return (0, _utils.loadGetInitialProps)(App, {\n            AppTree,\n            Component,\n            router: this,\n            ctx\n        });\n    }\n    get route() {\n        return this.state.route;\n    }\n    get pathname() {\n        return this.state.pathname;\n    }\n    get query() {\n        return this.state.query;\n    }\n    get asPath() {\n        return this.state.asPath;\n    }\n    get locale() {\n        return this.state.locale;\n    }\n    get isFallback() {\n        return this.state.isFallback;\n    }\n    get isPreview() {\n        return this.state.isPreview;\n    }\n    constructor(pathname, query, as, { initialProps, pageLoader, App, wrapApp, Component, err, subscription, isFallback, locale, locales, defaultLocale, domainLocales, isPreview }){\n        // Server Data Cache (full data requests)\n        this.sdc = {};\n        // Server Background Cache (HEAD requests)\n        this.sbc = {};\n        this.isFirstPopStateEvent = true;\n        this._key = createKey();\n        this.onPopState = (e)=>{\n            const { isFirstPopStateEvent } = this;\n            this.isFirstPopStateEvent = false;\n            const state = e.state;\n            if (!state) {\n                // We get state as undefined for two reasons.\n                //  1. With older safari (< 8) and older chrome (< 34)\n                //  2. When the URL changed with #\n                //\n                // In the both cases, we don't need to proceed and change the route.\n                // (as it's already changed)\n                // But we can simply replace the state with the new changes.\n                // Actually, for (1) we don't need to nothing. But it's hard to detect that event.\n                // So, doing the following for (1) does no harm.\n                const { pathname, query } = this;\n                this.changeState('replaceState', (0, _formaturl.formatWithValidation)({\n                    pathname: (0, _addbasepath.addBasePath)(pathname),\n                    query\n                }), (0, _utils.getURL)());\n                return;\n            }\n            // __NA is used to identify if the history entry can be handled by the app-router.\n            if (state.__NA) {\n                window.location.reload();\n                return;\n            }\n            if (!state.__N) {\n                return;\n            }\n            // Safari fires popstateevent when reopening the browser.\n            if (isFirstPopStateEvent && this.locale === state.options.locale && state.as === this.asPath) {\n                return;\n            }\n            let forcedScroll;\n            const { url, as, options, key } = state;\n            if (false) {}\n            this._key = key;\n            const { pathname } = (0, _parserelativeurl.parseRelativeUrl)(url);\n            // Make sure we don't re-render on initial load,\n            // can be caused by navigating back from an external site\n            if (this.isSsr && as === (0, _addbasepath.addBasePath)(this.asPath) && pathname === (0, _addbasepath.addBasePath)(this.pathname)) {\n                return;\n            }\n            // If the downstream application returns falsy, return.\n            // They will then be responsible for handling the event.\n            if (this._bps && !this._bps(state)) {\n                return;\n            }\n            this.change('replaceState', url, as, Object.assign({}, options, {\n                shallow: options.shallow && this._shallow,\n                locale: options.locale || this.defaultLocale,\n                // @ts-ignore internal value not exposed on types\n                _h: 0\n            }), forcedScroll);\n        };\n        // represents the current component key\n        const route = (0, _removetrailingslash.removeTrailingSlash)(pathname);\n        // set up the component cache (by route keys)\n        this.components = {};\n        // We should not keep the cache, if there's an error\n        // Otherwise, this cause issues when when going back and\n        // come again to the errored page.\n        if (pathname !== '/_error') {\n            this.components[route] = {\n                Component,\n                initial: true,\n                props: initialProps,\n                err,\n                __N_SSG: initialProps && initialProps.__N_SSG,\n                __N_SSP: initialProps && initialProps.__N_SSP\n            };\n        }\n        this.components['/_app'] = {\n            Component: App,\n            styleSheets: []\n        };\n        // Backwards compat for Router.router.events\n        // TODO: Should be remove the following major version as it was never documented\n        this.events = Router.events;\n        this.pageLoader = pageLoader;\n        // if auto prerendered and dynamic route wait to update asPath\n        // until after mount to prevent hydration mismatch\n        const autoExportDynamic = (0, _isdynamic.isDynamicRoute)(pathname) && self.__NEXT_DATA__.autoExport;\n        this.basePath =  false || '';\n        this.sub = subscription;\n        this.clc = null;\n        this._wrapApp = wrapApp;\n        // make sure to ignore extra popState in safari on navigating\n        // back from external site\n        this.isSsr = true;\n        this.isLocaleDomain = false;\n        this.isReady = !!(self.__NEXT_DATA__.gssp || self.__NEXT_DATA__.gip || self.__NEXT_DATA__.isExperimentalCompile || self.__NEXT_DATA__.appGip && !self.__NEXT_DATA__.gsp || !autoExportDynamic && !self.location.search && !false);\n        if (false) {}\n        this.state = {\n            route,\n            pathname,\n            query,\n            asPath: autoExportDynamic ? pathname : as,\n            isPreview: !!isPreview,\n            locale:  false ? 0 : undefined,\n            isFallback\n        };\n        this._initialMatchesMiddlewarePromise = Promise.resolve(false);\n        if (true) {\n            // make sure \"as\" doesn't start with double slashes or else it can\n            // throw an error as it's considered invalid\n            if (!as.startsWith('//')) {\n                // in order for `e.state` to work on the `onpopstate` event\n                // we have to register the initial route upon initialization\n                const options = {\n                    locale\n                };\n                const asPath = (0, _utils.getURL)();\n                this._initialMatchesMiddlewarePromise = matchesMiddleware({\n                    router: this,\n                    locale,\n                    asPath\n                }).then((matches)=>{\n                    // if middleware matches we leave resolving to the change function\n                    // as the server needs to resolve for correct priority\n                    ;\n                    options._shouldResolveHref = as !== pathname;\n                    this.changeState('replaceState', matches ? asPath : (0, _formaturl.formatWithValidation)({\n                        pathname: (0, _addbasepath.addBasePath)(pathname),\n                        query\n                    }), asPath, options);\n                    return matches;\n                });\n            }\n            window.addEventListener('popstate', this.onPopState);\n            // enable custom scroll restoration handling when available\n            // otherwise fallback to browser's default handling\n            if (false) {}\n        }\n    }\n}\nRouter.events = (0, _mitt.default)(); //# sourceMappingURL=router.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/shared/lib/router/router.js\n"));

/***/ })

});