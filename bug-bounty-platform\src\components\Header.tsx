'use client'

import { useState } from 'react'
import Link from 'next/link'
import { Menu, X, Shield, User, Search, Bell } from 'lucide-react'

export default function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false)

  return (
    <header className="bg-white shadow-sm border-b border-gray-200">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <div className="flex items-center">
            <Link href="/" className="flex items-center space-x-2">
              <Shield className="h-8 w-8 text-blue-600" />
              <span className="text-xl font-bold text-gray-900">BugBounty</span>
            </Link>
          </div>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-8">
            <Link href="/programs" className="text-gray-700 hover:text-blue-600 font-medium">
              Programs
            </Link>
            <Link href="/hackers" className="text-gray-700 hover:text-blue-600 font-medium">
              Hackers
            </Link>
            <Link href="/leaderboard" className="text-gray-700 hover:text-blue-600 font-medium">
              Leaderboard
            </Link>
            <Link href="/resources" className="text-gray-700 hover:text-blue-600 font-medium">
              Resources
            </Link>
          </nav>

          {/* Search and Actions */}
          <div className="hidden md:flex items-center space-x-4">
            <div className="relative">
              <Search className="h-5 w-5 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2" />
              <input
                type="text"
                placeholder="Search programs..."
                className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
            <button className="p-2 text-gray-400 hover:text-gray-600">
              <Bell className="h-5 w-5" />
            </button>
            <Link
              href="/login"
              className="flex items-center space-x-1 text-gray-700 hover:text-blue-600"
            >
              <User className="h-5 w-5" />
              <span>Sign In</span>
            </Link>
            <Link
              href="/signup"
              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
            >
              Get Started
            </Link>
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden">
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100"
            >
              {isMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
            </button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="md:hidden">
            <div className="px-2 pt-2 pb-3 space-y-1 sm:px-3 border-t border-gray-200">
              <Link
                href="/programs"
                className="block px-3 py-2 text-gray-700 hover:text-blue-600 font-medium"
              >
                Programs
              </Link>
              <Link
                href="/hackers"
                className="block px-3 py-2 text-gray-700 hover:text-blue-600 font-medium"
              >
                Hackers
              </Link>
              <Link
                href="/leaderboard"
                className="block px-3 py-2 text-gray-700 hover:text-blue-600 font-medium"
              >
                Leaderboard
              </Link>
              <Link
                href="/resources"
                className="block px-3 py-2 text-gray-700 hover:text-blue-600 font-medium"
              >
                Resources
              </Link>
              <div className="border-t border-gray-200 pt-4">
                <Link
                  href="/login"
                  className="block px-3 py-2 text-gray-700 hover:text-blue-600 font-medium"
                >
                  Sign In
                </Link>
                <Link
                  href="/signup"
                  className="block px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors mt-2"
                >
                  Get Started
                </Link>
              </div>
            </div>
          </div>
        )}
      </div>
    </header>
  )
}
