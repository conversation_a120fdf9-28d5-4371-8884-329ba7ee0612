import { Shield, Users, Globe, Award } from 'lucide-react'

export default function AboutHero() {
  return (
    <section className="relative py-20 overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0">
        <div className="absolute top-20 left-10 w-20 h-20 bg-gradient-to-r from-indigo-400 to-purple-400 rounded-full opacity-20 animate-pulse-slow"></div>
        <div className="absolute top-40 right-20 w-16 h-16 bg-gradient-to-r from-cyan-400 to-blue-400 rounded-full opacity-20 animate-pulse-slow" style={{animationDelay: '1s'}}></div>
        <div className="absolute bottom-40 left-20 w-12 h-12 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full opacity-20 animate-pulse-slow" style={{animationDelay: '2s'}}></div>
      </div>

      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h1 className="text-4xl md:text-6xl font-black text-gray-900 mb-6">
            About <span className="gradient-text">BugBounty</span>
          </h1>
          <p className="text-xl md:text-2xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
            We&apos;re on a mission to make the digital world safer by connecting organizations 
            with the world&apos;s most talented security researchers.
          </p>
        </div>

        {/* Mission Stats */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-8 max-w-4xl mx-auto">
          <div className="text-center group">
            <div className="card card-hover p-8 bg-gradient-to-br from-white to-indigo-50 border-indigo-100">
              <div className="relative mb-6">
                <Shield className="h-16 w-16 text-indigo-600 mx-auto group-hover:scale-110 transition-transform duration-300" />
                <div className="absolute inset-0 bg-indigo-600 rounded-full opacity-10 group-hover:opacity-20 transition-opacity duration-300 blur-xl"></div>
              </div>
              <div className="text-3xl font-black text-gray-900 mb-2">500+</div>
              <div className="text-gray-600 font-medium">Programs Protected</div>
            </div>
          </div>

          <div className="text-center group">
            <div className="card card-hover p-8 bg-gradient-to-br from-white to-emerald-50 border-emerald-100">
              <div className="relative mb-6">
                <Users className="h-16 w-16 text-emerald-600 mx-auto group-hover:scale-110 transition-transform duration-300" />
                <div className="absolute inset-0 bg-emerald-600 rounded-full opacity-10 group-hover:opacity-20 transition-opacity duration-300 blur-xl"></div>
              </div>
              <div className="text-3xl font-black text-gray-900 mb-2">50K+</div>
              <div className="text-gray-600 font-medium">Security Researchers</div>
            </div>
          </div>

          <div className="text-center group">
            <div className="card card-hover p-8 bg-gradient-to-br from-white to-purple-50 border-purple-100">
              <div className="relative mb-6">
                <Globe className="h-16 w-16 text-purple-600 mx-auto group-hover:scale-110 transition-transform duration-300" />
                <div className="absolute inset-0 bg-purple-600 rounded-full opacity-10 group-hover:opacity-20 transition-opacity duration-300 blur-xl"></div>
              </div>
              <div className="text-3xl font-black text-gray-900 mb-2">150+</div>
              <div className="text-gray-600 font-medium">Countries</div>
            </div>
          </div>

          <div className="text-center group">
            <div className="card card-hover p-8 bg-gradient-to-br from-white to-amber-50 border-amber-100">
              <div className="relative mb-6">
                <Award className="h-16 w-16 text-amber-600 mx-auto group-hover:scale-110 transition-transform duration-300" />
                <div className="absolute inset-0 bg-amber-600 rounded-full opacity-10 group-hover:opacity-20 transition-opacity duration-300 blur-xl"></div>
              </div>
              <div className="text-3xl font-black text-gray-900 mb-2">$50M+</div>
              <div className="text-gray-600 font-medium">Rewards Paid</div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
