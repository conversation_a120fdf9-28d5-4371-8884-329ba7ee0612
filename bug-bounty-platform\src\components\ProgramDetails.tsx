'use client'

import { useState } from 'react'
import {
  Star,
  DollarSign,
  Users,
  Calendar,
  Clock,
  Shield,
  AlertTriangle,
  CheckCircle,
  Flag
} from 'lucide-react'

interface ProgramDetailsProps {
  programId: string
}

export default function ProgramDetails({ programId }: ProgramDetailsProps) {
  const [activeTab, setActiveTab] = useState('overview')

  // Mock data - in real app, this would be fetched based on programId
  const program = {
    id: programId,
    company: 'TechCorp',
    logo: '🏢',
    title: 'Web Application Security',
    description: 'Find vulnerabilities in our main web application and API endpoints. We focus on critical security issues that could impact user data and system integrity.',
    minBounty: 500,
    maxBounty: 10000,
    participants: 1250,
    lastUpdated: '2 days ago',
    rating: 4.8,
    tags: ['Web App', 'API', 'Critical'],
    difficulty: 'Medium',
    responseTime: '2-3 days',
    verified: true,
    website: 'https://techcorp.com',
    scope: [
      'https://app.techcorp.com/*',
      'https://api.techcorp.com/*',
      'https://admin.techcorp.com/*'
    ],
    outOfScope: [
      'https://blog.techcorp.com/*',
      'Social engineering attacks',
      'Physical attacks',
      'DoS/DDoS attacks'
    ],
    rewards: [
      { severity: 'Critical', amount: '$5,000 - $10,000', description: 'Remote code execution, SQL injection leading to data breach' },
      { severity: 'High', amount: '$2,000 - $5,000', description: 'Authentication bypass, privilege escalation' },
      { severity: 'Medium', amount: '$500 - $2,000', description: 'XSS, CSRF, information disclosure' },
      { severity: 'Low', amount: '$100 - $500', description: 'Minor security misconfigurations' }
    ],
    rules: [
      'Do not access, modify, or delete user data',
      'Do not perform attacks that could impact service availability',
      'Report vulnerabilities through our platform only',
      'Do not publicly disclose vulnerabilities before they are fixed',
      'Respect user privacy and data protection laws'
    ],
    contact: {
      email: '<EMAIL>',
      responseTime: '2-3 business days',
      timezone: 'PST (UTC-8)'
    }
  }

  const tabs = [
    { id: 'overview', name: 'Overview' },
    { id: 'scope', name: 'Scope' },
    { id: 'rewards', name: 'Rewards' },
    { id: 'rules', name: 'Rules' },
    { id: 'reports', name: 'Reports' }
  ]

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'Easy': return 'bg-green-100 text-green-800'
      case 'Medium': return 'bg-yellow-100 text-yellow-800'
      case 'Hard': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'Critical': return 'text-red-600 bg-red-50'
      case 'High': return 'text-orange-600 bg-orange-50'
      case 'Medium': return 'text-yellow-600 bg-yellow-50'
      case 'Low': return 'text-blue-600 bg-blue-50'
      default: return 'text-gray-600 bg-gray-50'
    }
  }

  return (
    <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Program Header */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
        <div className="flex items-start justify-between mb-6">
          <div className="flex items-center space-x-4">
            <div className="text-4xl">{program.logo}</div>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">{program.company}</h1>
              <p className="text-lg text-gray-600">{program.title}</p>
              <div className="flex items-center space-x-4 mt-2">
                <div className="flex items-center space-x-1">
                  <Star className="h-4 w-4 text-yellow-400 fill-current" />
                  <span className="text-sm text-gray-600">{program.rating}</span>
                </div>
                {program.verified && (
                  <div className="flex items-center space-x-1">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    <span className="text-sm text-green-600">Verified</span>
                  </div>
                )}
                <span className={`px-2 py-1 text-xs rounded-full ${getDifficultyColor(program.difficulty)}`}>
                  {program.difficulty}
                </span>
              </div>
            </div>
          </div>
          <div className="flex space-x-3">
            <button className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors">
              Submit Report
            </button>
            <button className="border border-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-50 transition-colors">
              <Flag className="h-4 w-4" />
            </button>
          </div>
        </div>

        <p className="text-gray-600 mb-6">{program.description}</p>

        <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
          <div className="text-center">
            <DollarSign className="h-6 w-6 text-green-600 mx-auto mb-2" />
            <div className="text-lg font-semibold text-gray-900">
              ${program.minBounty.toLocaleString()} - ${program.maxBounty.toLocaleString()}
            </div>
            <div className="text-sm text-gray-500">Bounty Range</div>
          </div>
          <div className="text-center">
            <Users className="h-6 w-6 text-blue-600 mx-auto mb-2" />
            <div className="text-lg font-semibold text-gray-900">{program.participants.toLocaleString()}</div>
            <div className="text-sm text-gray-500">Participants</div>
          </div>
          <div className="text-center">
            <Clock className="h-6 w-6 text-purple-600 mx-auto mb-2" />
            <div className="text-lg font-semibold text-gray-900">{program.responseTime}</div>
            <div className="text-sm text-gray-500">Response Time</div>
          </div>
          <div className="text-center">
            <Calendar className="h-6 w-6 text-orange-600 mx-auto mb-2" />
            <div className="text-lg font-semibold text-gray-900">{program.lastUpdated}</div>
            <div className="text-sm text-gray-500">Last Updated</div>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8 px-6">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                {tab.name}
              </button>
            ))}
          </nav>
        </div>

        <div className="p-6">
          {activeTab === 'overview' && (
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-3">Program Overview</h3>
                <p className="text-gray-600">{program.description}</p>
              </div>

              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-3">Tags</h3>
                <div className="flex flex-wrap gap-2">
                  {program.tags.map((tag, index) => (
                    <span key={index} className="px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full">
                      {tag}
                    </span>
                  ))}
                </div>
              </div>

              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-3">Contact Information</h3>
                <div className="space-y-2">
                  <p><strong>Email:</strong> {program.contact.email}</p>
                  <p><strong>Response Time:</strong> {program.contact.responseTime}</p>
                  <p><strong>Timezone:</strong> {program.contact.timezone}</p>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'scope' && (
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-3 flex items-center">
                  <CheckCircle className="h-5 w-5 text-green-500 mr-2" />
                  In Scope
                </h3>
                <ul className="space-y-2">
                  {program.scope.map((item, index) => (
                    <li key={index} className="flex items-center space-x-2">
                      <span className="text-green-500">✓</span>
                      <code className="bg-gray-100 px-2 py-1 rounded text-sm">{item}</code>
                    </li>
                  ))}
                </ul>
              </div>

              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-3 flex items-center">
                  <AlertTriangle className="h-5 w-5 text-red-500 mr-2" />
                  Out of Scope
                </h3>
                <ul className="space-y-2">
                  {program.outOfScope.map((item, index) => (
                    <li key={index} className="flex items-center space-x-2">
                      <span className="text-red-500">✗</span>
                      <span className="text-gray-600">{item}</span>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          )}

          {activeTab === 'rewards' && (
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Reward Structure</h3>
              <div className="space-y-4">
                {program.rewards.map((reward, index) => (
                  <div key={index} className={`p-4 rounded-lg border ${getSeverityColor(reward.severity)}`}>
                    <div className="flex justify-between items-start mb-2">
                      <h4 className="font-semibold">{reward.severity}</h4>
                      <span className="font-bold">{reward.amount}</span>
                    </div>
                    <p className="text-sm">{reward.description}</p>
                  </div>
                ))}
              </div>
            </div>
          )}

          {activeTab === 'rules' && (
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Program Rules</h3>
              <ul className="space-y-3">
                {program.rules.map((rule, index) => (
                  <li key={index} className="flex items-start space-x-3">
                    <Shield className="h-5 w-5 text-blue-500 mt-0.5 flex-shrink-0" />
                    <span className="text-gray-600">{rule}</span>
                  </li>
                ))}
              </ul>
            </div>
          )}

          {activeTab === 'reports' && (
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Recent Reports</h3>
              <div className="text-center py-8">
                <p className="text-gray-500">No public reports available for this program.</p>
              </div>
            </div>
          )}
        </div>
      </div>
    </main>
  )
}
