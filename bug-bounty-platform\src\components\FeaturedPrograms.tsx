import Link from 'next/link'
import { ArrowRight, DollarSign, Users, Calendar, Star } from 'lucide-react'

export default function FeaturedPrograms() {
  const programs = [
    {
      id: 1,
      company: 'TechCorp',
      logo: '🏢',
      title: 'Web Application Security',
      description: 'Find vulnerabilities in our main web application and API endpoints.',
      minBounty: 500,
      maxBounty: 10000,
      participants: 1250,
      lastUpdated: '2 days ago',
      rating: 4.8,
      tags: ['Web App', 'API', 'Critical'],
      difficulty: 'Medium'
    },
    {
      id: 2,
      company: 'CloudSecure',
      logo: '☁️',
      title: 'Cloud Infrastructure',
      description: 'Security assessment of our cloud infrastructure and services.',
      minBounty: 1000,
      maxBounty: 25000,
      participants: 890,
      lastUpdated: '1 day ago',
      rating: 4.9,
      tags: ['Cloud', 'Infrastructure', 'High'],
      difficulty: 'Hard'
    },
    {
      id: 3,
      company: 'MobileFirst',
      logo: '📱',
      title: 'Mobile Application',
      description: 'iOS and Android app security testing for our fintech platform.',
      minBounty: 300,
      maxBounty: 8000,
      participants: 2100,
      lastUpdated: '3 hours ago',
      rating: 4.7,
      tags: ['Mobile', 'iOS', 'Android'],
      difficulty: 'Easy'
    },
    {
      id: 4,
      company: 'DataVault',
      logo: '🔒',
      title: 'Database Security',
      description: 'Comprehensive security review of our database systems.',
      minBounty: 800,
      maxBounty: 15000,
      participants: 650,
      lastUpdated: '5 hours ago',
      rating: 4.6,
      tags: ['Database', 'SQL', 'NoSQL'],
      difficulty: 'Medium'
    }
  ]

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'Easy': return 'bg-green-100 text-green-800'
      case 'Medium': return 'bg-yellow-100 text-yellow-800'
      case 'Hard': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <section className="py-16 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            Featured Bug Bounty Programs
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Discover high-quality programs from top companies looking for security researchers 
            to help them build more secure products.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
          {programs.map((program) => (
            <div key={program.id} className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <div className="text-2xl">{program.logo}</div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900">{program.company}</h3>
                    <p className="text-sm text-gray-500">{program.title}</p>
                  </div>
                </div>
                <div className="flex items-center space-x-1">
                  <Star className="h-4 w-4 text-yellow-400 fill-current" />
                  <span className="text-sm text-gray-600">{program.rating}</span>
                </div>
              </div>

              <p className="text-gray-600 mb-4">{program.description}</p>

              <div className="flex flex-wrap gap-2 mb-4">
                {program.tags.map((tag, index) => (
                  <span key={index} className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
                    {tag}
                  </span>
                ))}
                <span className={`px-2 py-1 text-xs rounded-full ${getDifficultyColor(program.difficulty)}`}>
                  {program.difficulty}
                </span>
              </div>

              <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                <div className="flex items-center space-x-4">
                  <div className="flex items-center space-x-1">
                    <DollarSign className="h-4 w-4" />
                    <span>${program.minBounty.toLocaleString()} - ${program.maxBounty.toLocaleString()}</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Users className="h-4 w-4" />
                    <span>{program.participants.toLocaleString()}</span>
                  </div>
                </div>
                <div className="flex items-center space-x-1">
                  <Calendar className="h-4 w-4" />
                  <span>{program.lastUpdated}</span>
                </div>
              </div>

              <Link
                href={`/programs/${program.id}`}
                className="inline-flex items-center text-blue-600 hover:text-blue-700 font-medium"
              >
                View Program
                <ArrowRight className="ml-1 h-4 w-4" />
              </Link>
            </div>
          ))}
        </div>

        <div className="text-center">
          <Link
            href="/programs"
            className="inline-flex items-center bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors"
          >
            View All Programs
            <ArrowRight className="ml-2 h-5 w-5" />
          </Link>
        </div>
      </div>
    </section>
  )
}
