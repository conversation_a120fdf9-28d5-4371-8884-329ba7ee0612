'use client'

import { useState } from 'react'
import { Trophy, Award, TrendingUp, TrendingDown, Minus, CheckCircle } from 'lucide-react'

export default function LeaderboardTable() {
  const [timeframe, setTimeframe] = useState('all-time')
  const [category, setCategory] = useState('overall')

  const leaderboardData = [
    {
      rank: 1,
      previousRank: 1,
      name: '<PERSON>',
      username: '@alexsec',
      avatar: '👨‍💻',
      country: '🇺🇸',
      totalEarnings: 125000,
      bugsFound: 342,
      reputation: 9.8,
      monthlyEarnings: 25000,
      verified: true,
      change: 0
    },
    {
      rank: 2,
      previousRank: 3,
      name: '<PERSON>',
      username: '@sarahj',
      avatar: '👩‍💻',
      country: '🇬🇧',
      totalEarnings: 98500,
      bugsFound: 289,
      reputation: 9.7,
      monthlyEarnings: 18500,
      verified: true,
      change: 1
    },
    {
      rank: 3,
      previousRank: 2,
      name: '<PERSON>',
      username: '@rajsec',
      avatar: '👨‍💻',
      country: '🇮🇳',
      totalEarnings: 87200,
      bugsFound: 256,
      reputation: 9.6,
      monthlyEarnings: 15200,
      verified: true,
      change: -1
    },
    {
      rank: 4,
      previousRank: 4,
      name: '<PERSON>',
      username: '@mariag',
      avatar: '👩‍💻',
      country: '🇪🇸',
      totalEarnings: 76800,
      bugsFound: 198,
      reputation: 9.5,
      monthlyEarnings: 12800,
      verified: true,
      change: 0
    },
    {
      rank: 5,
      previousRank: 6,
      name: 'David Kim',
      username: '@davidk',
      avatar: '👨‍💻',
      country: '🇰🇷',
      totalEarnings: 65400,
      bugsFound: 167,
      reputation: 9.4,
      monthlyEarnings: 11400,
      verified: true,
      change: 1
    },
    {
      rank: 6,
      previousRank: 5,
      name: 'Emma Wilson',
      username: '@emmaw',
      avatar: '👩‍💻',
      country: '🇨🇦',
      totalEarnings: 54300,
      bugsFound: 145,
      reputation: 9.3,
      monthlyEarnings: 9300,
      verified: true,
      change: -1
    },
    {
      rank: 7,
      previousRank: 8,
      name: 'Lucas Silva',
      username: '@lucass',
      avatar: '👨‍💻',
      country: '🇧🇷',
      totalEarnings: 48900,
      bugsFound: 134,
      reputation: 9.2,
      monthlyEarnings: 8900,
      verified: true,
      change: 1
    },
    {
      rank: 8,
      previousRank: 7,
      name: 'Anna Kowalski',
      username: '@annak',
      avatar: '👩‍💻',
      country: '🇵🇱',
      totalEarnings: 43200,
      bugsFound: 123,
      reputation: 9.1,
      monthlyEarnings: 7200,
      verified: true,
      change: -1
    },
    {
      rank: 9,
      previousRank: 9,
      name: 'Ahmed Hassan',
      username: '@ahmedh',
      avatar: '👨‍💻',
      country: '🇪🇬',
      totalEarnings: 39800,
      bugsFound: 112,
      reputation: 9.0,
      monthlyEarnings: 6800,
      verified: true,
      change: 0
    },
    {
      rank: 10,
      previousRank: 11,
      name: 'Sophie Martin',
      username: '@sophiem',
      avatar: '👩‍💻',
      country: '🇫🇷',
      totalEarnings: 36500,
      bugsFound: 98,
      reputation: 8.9,
      monthlyEarnings: 5500,
      verified: true,
      change: 1
    }
  ]

  const getRankIcon = (rank: number) => {
    switch (rank) {
      case 1: return <Trophy className="h-6 w-6 text-yellow-500" />
      case 2: return <Award className="h-6 w-6 text-gray-400" />
      case 3: return <Award className="h-6 w-6 text-amber-600" />
      default: return <span className="text-lg font-bold text-gray-600">#{rank}</span>
    }
  }

  const getChangeIcon = (change: number) => {
    if (change > 0) return <TrendingUp className="h-4 w-4 text-green-500" />
    if (change < 0) return <TrendingDown className="h-4 w-4 text-red-500" />
    return <Minus className="h-4 w-4 text-gray-400" />
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200">
      {/* Filters */}
      <div className="p-6 border-b border-gray-200">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-4 sm:space-y-0">
          <div className="flex space-x-4">
            <select
              value={timeframe}
              onChange={(e) => setTimeframe(e.target.value)}
              className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="all-time">All Time</option>
              <option value="this-year">This Year</option>
              <option value="this-month">This Month</option>
              <option value="this-week">This Week</option>
            </select>
            <select
              value={category}
              onChange={(e) => setCategory(e.target.value)}
              className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="overall">Overall</option>
              <option value="earnings">By Earnings</option>
              <option value="bugs">By Bugs Found</option>
              <option value="reputation">By Reputation</option>
            </select>
          </div>
          <div className="text-sm text-gray-500">
            Updated 2 hours ago
          </div>
        </div>
      </div>

      {/* Table */}
      <div className="overflow-x-auto">
        <table className="w-full">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Rank
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Researcher
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Total Earnings
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Bugs Found
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Reputation
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                This Month
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Change
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {leaderboardData.map((researcher) => (
              <tr key={researcher.rank} className="hover:bg-gray-50">
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center space-x-3">
                    {getRankIcon(researcher.rank)}
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center space-x-3">
                    <div className="text-2xl">{researcher.avatar}</div>
                    <div>
                      <div className="flex items-center space-x-2">
                        <div className="text-sm font-medium text-gray-900">{researcher.name}</div>
                        {researcher.verified && (
                          <CheckCircle className="h-4 w-4 text-blue-500" />
                        )}
                      </div>
                      <div className="flex items-center space-x-1">
                        <div className="text-sm text-gray-500">{researcher.username}</div>
                        <span className="text-lg">{researcher.country}</span>
                      </div>
                    </div>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm font-medium text-gray-900">
                    ${researcher.totalEarnings.toLocaleString()}
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-gray-900">{researcher.bugsFound}</div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center space-x-1">
                    <span className="text-yellow-400">⭐</span>
                    <span className="text-sm font-medium text-gray-900">{researcher.reputation}</span>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-gray-900">
                    ${researcher.monthlyEarnings.toLocaleString()}
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center space-x-1">
                    {getChangeIcon(researcher.change)}
                    <span className="text-sm text-gray-500">
                      {researcher.change === 0 ? '-' : Math.abs(researcher.change)}
                    </span>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      <div className="px-6 py-4 border-t border-gray-200">
        <div className="flex items-center justify-between">
          <div className="text-sm text-gray-500">
            Showing 1 to 10 of 5,000+ researchers
          </div>
          <nav className="flex items-center space-x-2">
            <button className="px-3 py-2 text-gray-500 hover:text-gray-700">Previous</button>
            <button className="px-3 py-2 bg-blue-600 text-white rounded">1</button>
            <button className="px-3 py-2 text-gray-500 hover:text-gray-700">2</button>
            <button className="px-3 py-2 text-gray-500 hover:text-gray-700">3</button>
            <span className="px-3 py-2 text-gray-500">...</span>
            <button className="px-3 py-2 text-gray-500 hover:text-gray-700">500</button>
            <button className="px-3 py-2 text-gray-500 hover:text-gray-700">Next</button>
          </nav>
        </div>
      </div>
    </div>
  )
}
