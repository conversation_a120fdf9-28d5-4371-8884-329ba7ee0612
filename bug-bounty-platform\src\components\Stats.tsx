import { TrendingUp, Clock, Target, Globe } from 'lucide-react'

export default function Stats() {
  const stats = [
    {
      icon: TrendingUp,
      value: '2.5M+',
      label: 'Total Bounties Paid',
      description: 'Across all programs',
      color: 'text-green-600'
    },
    {
      icon: Clock,
      value: '24/7',
      label: 'Continuous Security',
      description: 'Round-the-clock protection',
      color: 'text-blue-600'
    },
    {
      icon: Target,
      value: '99.9%',
      label: 'Accuracy Rate',
      description: 'Valid vulnerability reports',
      color: 'text-purple-600'
    },
    {
      icon: Globe,
      value: '150+',
      label: 'Countries',
      description: 'Global hacker community',
      color: 'text-orange-600'
    }
  ]

  return (
    <section className="py-20 bg-gradient-to-br from-white to-indigo-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-black text-gray-900 mb-6">
            Trusted by <span className="gradient-text">Industry Leaders</span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Our platform connects organizations with the world&apos;s most talented security researchers
            to identify and fix vulnerabilities before they can be exploited.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {stats.map((stat, index) => {
            const IconComponent = stat.icon
            return (
              <div key={index} className="text-center group">
                <div className="card card-hover p-8 bg-gradient-to-br from-white to-gray-50 border-gray-100">
                  <div className="relative mb-6">
                    <IconComponent className={`h-16 w-16 ${stat.color} mx-auto group-hover:scale-110 transition-transform duration-300`} />
                    <div className={`absolute inset-0 ${stat.color.replace('text-', 'bg-').replace('-600', '-600')} rounded-full opacity-10 group-hover:opacity-20 transition-opacity duration-300 blur-xl`}></div>
                  </div>
                  <div className="text-4xl font-black text-gray-900 mb-3">{stat.value}</div>
                  <div className="text-lg font-bold text-gray-700 mb-2">{stat.label}</div>
                  <div className="text-sm text-gray-600">{stat.description}</div>
                </div>
              </div>
            )
          })}
        </div>

        {/* Company Logos */}
        <div className="mt-20">
          <p className="text-center text-gray-500 mb-12 text-lg">Trusted by leading companies worldwide</p>
          <div className="grid grid-cols-2 md:grid-cols-6 gap-8 items-center">
            {/* Placeholder for company logos */}
            <div className="bg-white h-16 rounded-xl shadow-sm border border-gray-100 flex items-center justify-center hover:shadow-md transition-shadow group">
              <span className="text-gray-600 font-bold text-lg group-hover:text-indigo-600 transition-colors">Microsoft</span>
            </div>
            <div className="bg-white h-16 rounded-xl shadow-sm border border-gray-100 flex items-center justify-center hover:shadow-md transition-shadow group">
              <span className="text-gray-600 font-bold text-lg group-hover:text-indigo-600 transition-colors">Google</span>
            </div>
            <div className="bg-white h-16 rounded-xl shadow-sm border border-gray-100 flex items-center justify-center hover:shadow-md transition-shadow group">
              <span className="text-gray-600 font-bold text-lg group-hover:text-indigo-600 transition-colors">Apple</span>
            </div>
            <div className="bg-white h-16 rounded-xl shadow-sm border border-gray-100 flex items-center justify-center hover:shadow-md transition-shadow group">
              <span className="text-gray-600 font-bold text-lg group-hover:text-indigo-600 transition-colors">Meta</span>
            </div>
            <div className="bg-white h-16 rounded-xl shadow-sm border border-gray-100 flex items-center justify-center hover:shadow-md transition-shadow group">
              <span className="text-gray-600 font-bold text-lg group-hover:text-indigo-600 transition-colors">Tesla</span>
            </div>
            <div className="bg-white h-16 rounded-xl shadow-sm border border-gray-100 flex items-center justify-center hover:shadow-md transition-shadow group">
              <span className="text-gray-600 font-bold text-lg group-hover:text-indigo-600 transition-colors">Netflix</span>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
