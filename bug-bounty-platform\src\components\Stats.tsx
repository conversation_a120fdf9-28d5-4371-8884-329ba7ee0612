import { TrendingUp, Clock, Target, Globe } from 'lucide-react'

export default function Stats() {
  const stats = [
    {
      icon: TrendingUp,
      value: '2.5M+',
      label: 'Total Bounties Paid',
      description: 'Across all programs',
      color: 'text-green-600'
    },
    {
      icon: Clock,
      value: '24/7',
      label: 'Continuous Security',
      description: 'Round-the-clock protection',
      color: 'text-blue-600'
    },
    {
      icon: Target,
      value: '99.9%',
      label: 'Accuracy Rate',
      description: 'Valid vulnerability reports',
      color: 'text-purple-600'
    },
    {
      icon: Globe,
      value: '150+',
      label: 'Countries',
      description: 'Global hacker community',
      color: 'text-orange-600'
    }
  ]

  return (
    <section className="py-16 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            Trusted by Industry Leaders
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Our platform connects organizations with the world's most talented security researchers 
            to identify and fix vulnerabilities before they can be exploited.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {stats.map((stat, index) => {
            const IconComponent = stat.icon
            return (
              <div key={index} className="text-center group">
                <div className="bg-gray-50 rounded-lg p-8 hover:bg-gray-100 transition-colors">
                  <IconComponent className={`h-12 w-12 ${stat.color} mx-auto mb-4`} />
                  <div className="text-3xl font-bold text-gray-900 mb-2">{stat.value}</div>
                  <div className="text-lg font-semibold text-gray-700 mb-1">{stat.label}</div>
                  <div className="text-sm text-gray-500">{stat.description}</div>
                </div>
              </div>
            )
          })}
        </div>

        {/* Company Logos */}
        <div className="mt-16">
          <p className="text-center text-gray-500 mb-8">Trusted by leading companies worldwide</p>
          <div className="grid grid-cols-2 md:grid-cols-6 gap-8 items-center opacity-60">
            {/* Placeholder for company logos */}
            <div className="bg-gray-200 h-12 rounded flex items-center justify-center">
              <span className="text-gray-500 font-semibold">Microsoft</span>
            </div>
            <div className="bg-gray-200 h-12 rounded flex items-center justify-center">
              <span className="text-gray-500 font-semibold">Google</span>
            </div>
            <div className="bg-gray-200 h-12 rounded flex items-center justify-center">
              <span className="text-gray-500 font-semibold">Apple</span>
            </div>
            <div className="bg-gray-200 h-12 rounded flex items-center justify-center">
              <span className="text-gray-500 font-semibold">Meta</span>
            </div>
            <div className="bg-gray-200 h-12 rounded flex items-center justify-center">
              <span className="text-gray-500 font-semibold">Tesla</span>
            </div>
            <div className="bg-gray-200 h-12 rounded flex items-center justify-center">
              <span className="text-gray-500 font-semibold">Netflix</span>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
