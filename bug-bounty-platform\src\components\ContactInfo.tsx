import { 
  MapPin, 
  Phone, 
  Mail, 
  Clock, 
  MessageCircle, 
  Twitter, 
  Github, 
  Linkedin,
  HelpCircle,
  Shield,
  Users
} from 'lucide-react'

export default function ContactInfo() {
  const contactMethods = [
    {
      icon: Mail,
      title: 'Email Support',
      description: 'Get help from our support team',
      contact: '<EMAIL>',
      action: 'mailto:<EMAIL>',
      color: 'text-blue-600 bg-blue-50'
    },
    {
      icon: Phone,
      title: 'Phone Support',
      description: 'Speak with our team directly',
      contact: '+****************',
      action: 'tel:+15551234567',
      color: 'text-green-600 bg-green-50'
    },
    {
      icon: MessageCircle,
      title: 'Live Chat',
      description: 'Chat with us in real-time',
      contact: 'Available 24/7',
      action: '#',
      color: 'text-purple-600 bg-purple-50'
    }
  ]

  const quickLinks = [
    {
      icon: HelpCircle,
      title: 'Help Center',
      description: 'Find answers to common questions',
      link: '/help'
    },
    {
      icon: Shield,
      title: 'Security',
      description: 'Report security vulnerabilities',
      link: '/responsible-disclosure'
    },
    {
      icon: Users,
      title: 'Community',
      description: 'Join our community forums',
      link: '/community'
    }
  ]

  const socialLinks = [
    { icon: Twitter, name: 'Twitter', url: '#', color: 'hover:text-blue-400' },
    { icon: Github, name: 'GitHub', url: '#', color: 'hover:text-gray-900' },
    { icon: Linkedin, name: 'LinkedIn', url: '#', color: 'hover:text-blue-600' }
  ]

  return (
    <div className="space-y-8">
      {/* Contact Methods */}
      <div className="card p-8 bg-gradient-to-br from-white to-gray-50">
        <h2 className="text-xl font-bold text-gray-900 mb-6">Contact Information</h2>
        <div className="space-y-6">
          {contactMethods.map((method, index) => {
            const IconComponent = method.icon
            return (
              <div key={index} className="flex items-start space-x-4">
                <div className={`p-3 rounded-xl ${method.color}`}>
                  <IconComponent className="h-6 w-6" />
                </div>
                <div className="flex-1">
                  <h3 className="font-semibold text-gray-900 mb-1">{method.title}</h3>
                  <p className="text-sm text-gray-600 mb-2">{method.description}</p>
                  <a 
                    href={method.action}
                    className="text-indigo-600 hover:text-indigo-700 font-medium text-sm"
                  >
                    {method.contact}
                  </a>
                </div>
              </div>
            )
          })}
        </div>
      </div>

      {/* Office Information */}
      <div className="card p-8 bg-gradient-to-br from-white to-indigo-50 border-indigo-100">
        <h2 className="text-xl font-bold text-gray-900 mb-6">Our Office</h2>
        <div className="space-y-4">
          <div className="flex items-start space-x-3">
            <MapPin className="h-5 w-5 text-indigo-600 mt-1" />
            <div>
              <div className="font-medium text-gray-900">Headquarters</div>
              <div className="text-gray-600">
                123 Security Street<br />
                San Francisco, CA 94105<br />
                United States
              </div>
            </div>
          </div>
          
          <div className="flex items-start space-x-3">
            <Clock className="h-5 w-5 text-indigo-600 mt-1" />
            <div>
              <div className="font-medium text-gray-900">Business Hours</div>
              <div className="text-gray-600">
                Monday - Friday: 9:00 AM - 6:00 PM PST<br />
                Saturday - Sunday: Closed<br />
                <span className="text-sm text-indigo-600">Emergency support available 24/7</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Quick Links */}
      <div className="card p-8 bg-gradient-to-br from-white to-emerald-50 border-emerald-100">
        <h2 className="text-xl font-bold text-gray-900 mb-6">Quick Links</h2>
        <div className="space-y-4">
          {quickLinks.map((link, index) => {
            const IconComponent = link.icon
            return (
              <a 
                key={index}
                href={link.link}
                className="flex items-center space-x-3 p-3 rounded-lg hover:bg-emerald-100 transition-colors group"
              >
                <IconComponent className="h-5 w-5 text-emerald-600 group-hover:scale-110 transition-transform" />
                <div>
                  <div className="font-medium text-gray-900 group-hover:text-emerald-700">{link.title}</div>
                  <div className="text-sm text-gray-600">{link.description}</div>
                </div>
              </a>
            )
          })}
        </div>
      </div>

      {/* Social Media */}
      <div className="card p-8 bg-gradient-to-br from-white to-purple-50 border-purple-100">
        <h2 className="text-xl font-bold text-gray-900 mb-6">Follow Us</h2>
        <div className="flex space-x-4">
          {socialLinks.map((social, index) => {
            const IconComponent = social.icon
            return (
              <a
                key={index}
                href={social.url}
                className={`p-3 bg-white rounded-xl border border-gray-200 text-gray-600 ${social.color} hover:shadow-md transition-all duration-200 group`}
                title={social.name}
              >
                <IconComponent className="h-6 w-6 group-hover:scale-110 transition-transform" />
              </a>
            )
          })}
        </div>
        <p className="text-sm text-gray-600 mt-4">
          Stay updated with the latest news, security tips, and platform updates.
        </p>
      </div>

      {/* Emergency Contact */}
      <div className="bg-red-50 border border-red-200 rounded-xl p-6">
        <div className="flex items-start space-x-3">
          <Shield className="h-6 w-6 text-red-600 mt-1" />
          <div>
            <h3 className="font-semibold text-red-900 mb-2">Security Emergency</h3>
            <p className="text-sm text-red-800 mb-3">
              If you&apos;ve discovered a critical security vulnerability that poses immediate risk, 
              please contact us immediately.
            </p>
            <a 
              href="mailto:<EMAIL>"
              className="inline-flex items-center text-red-700 hover:text-red-800 font-medium text-sm"
            >
              <Mail className="h-4 w-4 mr-2" />
              <EMAIL>
            </a>
          </div>
        </div>
      </div>
    </div>
  )
}
