[{"C:\\languege_arabic\\bug-bounty-platform\\src\\app\\dashboard\\page.tsx": "1", "C:\\languege_arabic\\bug-bounty-platform\\src\\app\\hackers\\page.tsx": "2", "C:\\languege_arabic\\bug-bounty-platform\\src\\app\\layout.tsx": "3", "C:\\languege_arabic\\bug-bounty-platform\\src\\app\\leaderboard\\page.tsx": "4", "C:\\languege_arabic\\bug-bounty-platform\\src\\app\\login\\page.tsx": "5", "C:\\languege_arabic\\bug-bounty-platform\\src\\app\\page.tsx": "6", "C:\\languege_arabic\\bug-bounty-platform\\src\\app\\programs\\page.tsx": "7", "C:\\languege_arabic\\bug-bounty-platform\\src\\app\\programs\\[id]\\page.tsx": "8", "C:\\languege_arabic\\bug-bounty-platform\\src\\app\\signup\\page.tsx": "9", "C:\\languege_arabic\\bug-bounty-platform\\src\\components\\DashboardOverview.tsx": "10", "C:\\languege_arabic\\bug-bounty-platform\\src\\components\\DashboardSidebar.tsx": "11", "C:\\languege_arabic\\bug-bounty-platform\\src\\components\\FeaturedPrograms.tsx": "12", "C:\\languege_arabic\\bug-bounty-platform\\src\\components\\Footer.tsx": "13", "C:\\languege_arabic\\bug-bounty-platform\\src\\components\\HackersFilters.tsx": "14", "C:\\languege_arabic\\bug-bounty-platform\\src\\components\\HackersGrid.tsx": "15", "C:\\languege_arabic\\bug-bounty-platform\\src\\components\\Header.tsx": "16", "C:\\languege_arabic\\bug-bounty-platform\\src\\components\\Hero.tsx": "17", "C:\\languege_arabic\\bug-bounty-platform\\src\\components\\LeaderboardStats.tsx": "18", "C:\\languege_arabic\\bug-bounty-platform\\src\\components\\LeaderboardTable.tsx": "19", "C:\\languege_arabic\\bug-bounty-platform\\src\\components\\LoginForm.tsx": "20", "C:\\languege_arabic\\bug-bounty-platform\\src\\components\\ProgramDetails.tsx": "21", "C:\\languege_arabic\\bug-bounty-platform\\src\\components\\ProgramsFilters.tsx": "22", "C:\\languege_arabic\\bug-bounty-platform\\src\\components\\ProgramsGrid.tsx": "23", "C:\\languege_arabic\\bug-bounty-platform\\src\\components\\SignupForm.tsx": "24", "C:\\languege_arabic\\bug-bounty-platform\\src\\components\\Stats.tsx": "25", "C:\\languege_arabic\\bug-bounty-platform\\src\\components\\TopHackers.tsx": "26", "C:\\languege_arabic\\bug-bounty-platform\\src\\app\\profile\\page.tsx": "27", "C:\\languege_arabic\\bug-bounty-platform\\src\\app\\resources\\page.tsx": "28", "C:\\languege_arabic\\bug-bounty-platform\\src\\app\\settings\\page.tsx": "29", "C:\\languege_arabic\\bug-bounty-platform\\src\\components\\ProfileContent.tsx": "30", "C:\\languege_arabic\\bug-bounty-platform\\src\\components\\ProfileHeader.tsx": "31", "C:\\languege_arabic\\bug-bounty-platform\\src\\components\\ResourcesGrid.tsx": "32", "C:\\languege_arabic\\bug-bounty-platform\\src\\components\\ResourcesHero.tsx": "33", "C:\\languege_arabic\\bug-bounty-platform\\src\\components\\SettingsContent.tsx": "34"}, {"size": 710, "mtime": 1748115989554, "results": "35", "hashOfConfig": "36"}, {"size": 980, "mtime": 1748115647258, "results": "37", "hashOfConfig": "36"}, {"size": 689, "mtime": 1748114957503, "results": "38", "hashOfConfig": "36"}, {"size": 800, "mtime": 1748115814014, "results": "39", "hashOfConfig": "36"}, {"size": 474, "mtime": 1748115904138, "results": "40", "hashOfConfig": "36"}, {"size": 532, "mtime": 1748117331985, "results": "41", "hashOfConfig": "36"}, {"size": 1009, "mtime": 1748115499044, "results": "42", "hashOfConfig": "36"}, {"size": 465, "mtime": 1748116312201, "results": "43", "hashOfConfig": "36"}, {"size": 478, "mtime": 1748115936988, "results": "44", "hashOfConfig": "36"}, {"size": 9624, "mtime": 1748116407962, "results": "45", "hashOfConfig": "36"}, {"size": 3283, "mtime": 1748116420181, "results": "46", "hashOfConfig": "36"}, {"size": 5728, "mtime": 1748117375567, "results": "47", "hashOfConfig": "36"}, {"size": 6785, "mtime": 1748116429470, "results": "48", "hashOfConfig": "36"}, {"size": 6519, "mtime": 1748115678286, "results": "49", "hashOfConfig": "36"}, {"size": 10639, "mtime": 1748115733537, "results": "50", "hashOfConfig": "36"}, {"size": 6340, "mtime": 1748117018750, "results": "51", "hashOfConfig": "36"}, {"size": 6225, "mtime": 1748117101853, "results": "52", "hashOfConfig": "36"}, {"size": 1571, "mtime": 1748116455710, "results": "53", "hashOfConfig": "36"}, {"size": 10511, "mtime": 1748116463658, "results": "54", "hashOfConfig": "36"}, {"size": 5197, "mtime": 1748116473092, "results": "55", "hashOfConfig": "36"}, {"size": 11472, "mtime": 1748116485134, "results": "56", "hashOfConfig": "36"}, {"size": 6101, "mtime": 1748115526704, "results": "57", "hashOfConfig": "36"}, {"size": 9755, "mtime": 1748115574337, "results": "58", "hashOfConfig": "36"}, {"size": 10901, "mtime": 1748116493856, "results": "59", "hashOfConfig": "36"}, {"size": 4764, "mtime": 1748117492066, "results": "60", "hashOfConfig": "36"}, {"size": 6879, "mtime": 1748117434601, "results": "61", "hashOfConfig": "36"}, {"size": 487, "mtime": 1748117184291, "results": "62", "hashOfConfig": "36"}, {"size": 391, "mtime": 1748117115390, "results": "63", "hashOfConfig": "36"}, {"size": 683, "mtime": 1748117267687, "results": "64", "hashOfConfig": "36"}, {"size": 9245, "mtime": 1748117608390, "results": "65", "hashOfConfig": "36"}, {"size": 5884, "mtime": 1748117212418, "results": "66", "hashOfConfig": "36"}, {"size": 9309, "mtime": 1748117175035, "results": "67", "hashOfConfig": "36"}, {"size": 3023, "mtime": 1748117132502, "results": "68", "hashOfConfig": "36"}, {"size": 14409, "mtime": 1748117622477, "results": "69", "hashOfConfig": "36"}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1jy221m", {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "157", "messages": "158", "suppressedMessages": "159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "160", "messages": "161", "suppressedMessages": "162", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "163", "messages": "164", "suppressedMessages": "165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "166", "messages": "167", "suppressedMessages": "168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "169", "messages": "170", "suppressedMessages": "171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\languege_arabic\\bug-bounty-platform\\src\\app\\dashboard\\page.tsx", [], [], "C:\\languege_arabic\\bug-bounty-platform\\src\\app\\hackers\\page.tsx", [], [], "C:\\languege_arabic\\bug-bounty-platform\\src\\app\\layout.tsx", [], [], "C:\\languege_arabic\\bug-bounty-platform\\src\\app\\leaderboard\\page.tsx", [], [], "C:\\languege_arabic\\bug-bounty-platform\\src\\app\\login\\page.tsx", [], [], "C:\\languege_arabic\\bug-bounty-platform\\src\\app\\page.tsx", [], [], "C:\\languege_arabic\\bug-bounty-platform\\src\\app\\programs\\page.tsx", [], [], "C:\\languege_arabic\\bug-bounty-platform\\src\\app\\programs\\[id]\\page.tsx", [], [], "C:\\languege_arabic\\bug-bounty-platform\\src\\app\\signup\\page.tsx", [], [], "C:\\languege_arabic\\bug-bounty-platform\\src\\components\\DashboardOverview.tsx", [], [], "C:\\languege_arabic\\bug-bounty-platform\\src\\components\\DashboardSidebar.tsx", [], [], "C:\\languege_arabic\\bug-bounty-platform\\src\\components\\FeaturedPrograms.tsx", [], [], "C:\\languege_arabic\\bug-bounty-platform\\src\\components\\Footer.tsx", [], [], "C:\\languege_arabic\\bug-bounty-platform\\src\\components\\HackersFilters.tsx", [], [], "C:\\languege_arabic\\bug-bounty-platform\\src\\components\\HackersGrid.tsx", [], [], "C:\\languege_arabic\\bug-bounty-platform\\src\\components\\Header.tsx", [], [], "C:\\languege_arabic\\bug-bounty-platform\\src\\components\\Hero.tsx", [], [], "C:\\languege_arabic\\bug-bounty-platform\\src\\components\\LeaderboardStats.tsx", [], [], "C:\\languege_arabic\\bug-bounty-platform\\src\\components\\LeaderboardTable.tsx", [], [], "C:\\languege_arabic\\bug-bounty-platform\\src\\components\\LoginForm.tsx", [], [], "C:\\languege_arabic\\bug-bounty-platform\\src\\components\\ProgramDetails.tsx", [], [], "C:\\languege_arabic\\bug-bounty-platform\\src\\components\\ProgramsFilters.tsx", [], [], "C:\\languege_arabic\\bug-bounty-platform\\src\\components\\ProgramsGrid.tsx", [], [], "C:\\languege_arabic\\bug-bounty-platform\\src\\components\\SignupForm.tsx", [], [], "C:\\languege_arabic\\bug-bounty-platform\\src\\components\\Stats.tsx", [], [], "C:\\languege_arabic\\bug-bounty-platform\\src\\components\\TopHackers.tsx", [], [], "C:\\languege_arabic\\bug-bounty-platform\\src\\app\\profile\\page.tsx", [], [], "C:\\languege_arabic\\bug-bounty-platform\\src\\app\\resources\\page.tsx", [], [], "C:\\languege_arabic\\bug-bounty-platform\\src\\app\\settings\\page.tsx", [], [], "C:\\languege_arabic\\bug-bounty-platform\\src\\components\\ProfileContent.tsx", [], [], "C:\\languege_arabic\\bug-bounty-platform\\src\\components\\ProfileHeader.tsx", [], [], "C:\\languege_arabic\\bug-bounty-platform\\src\\components\\ResourcesGrid.tsx", [], [], "C:\\languege_arabic\\bug-bounty-platform\\src\\components\\ResourcesHero.tsx", [], [], "C:\\languege_arabic\\bug-bounty-platform\\src\\components\\SettingsContent.tsx", [], []]