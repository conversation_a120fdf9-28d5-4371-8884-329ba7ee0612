import { NextRequest, NextResponse } from 'next/server'

// Mock database for programs (same as in route.ts)
const programs = [
  {
    id: 1,
    company: 'TechCorp',
    logo: '🏢',
    title: 'Web Application Security',
    description: 'Find vulnerabilities in our main web application and API endpoints. We focus on critical security issues that could impact user data.',
    minBounty: 500,
    maxBounty: 10000,
    participants: 1250,
    lastUpdated: '2 days ago',
    rating: 4.8,
    tags: ['Web App', 'API', 'Critical'],
    difficulty: 'Medium',
    responseTime: '2-3 days',
    verified: true,
    status: 'active',
    scope: [
      'https://app.techcorp.com/*',
      'https://api.techcorp.com/*',
      'https://admin.techcorp.com/*'
    ],
    outOfScope: [
      'https://blog.techcorp.com/*',
      'Social engineering attacks',
      'Physical attacks',
      'DoS/DDoS attacks'
    ],
    rewards: [
      { severity: 'Critical', amount: '$5,000 - $10,000', description: 'Remote code execution, SQL injection leading to data breach' },
      { severity: 'High', amount: '$2,000 - $5,000', description: 'Authentication bypass, privilege escalation' },
      { severity: 'Medium', amount: '$500 - $2,000', description: 'XSS, CSRF, information disclosure' },
      { severity: 'Low', amount: '$100 - $500', description: 'Minor security misconfigurations' }
    ],
    rules: [
      'Do not access, modify, or delete user data',
      'Do not perform attacks that could impact service availability',
      'Report vulnerabilities through our platform only',
      'Do not publicly disclose vulnerabilities before they are fixed',
      'Respect user privacy and data protection laws'
    ],
    contact: {
      email: '<EMAIL>',
      responseTime: '2-3 business days',
      timezone: 'PST (UTC-8)'
    },
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-15T00:00:00Z'
  }
]

interface RouteParams {
  params: Promise<{
    id: string
  }>
}

export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const { id } = await params
    const programId = parseInt(id)

    if (isNaN(programId)) {
      return NextResponse.json(
        { error: 'Invalid program ID' },
        { status: 400 }
      )
    }

    const program = programs.find(p => p.id === programId)

    if (!program) {
      return NextResponse.json(
        { error: 'Program not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      data: program
    })

  } catch (error) {
    console.error('Get program error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest, { params }: RouteParams) {
  try {
    const { id } = await params
    const programId = parseInt(id)
    const updateData = await request.json()

    if (isNaN(programId)) {
      return NextResponse.json(
        { error: 'Invalid program ID' },
        { status: 400 }
      )
    }

    const programIndex = programs.findIndex(p => p.id === programId)

    if (programIndex === -1) {
      return NextResponse.json(
        { error: 'Program not found' },
        { status: 404 }
      )
    }

    // Update program
    programs[programIndex] = {
      ...programs[programIndex],
      ...updateData,
      updatedAt: new Date().toISOString()
    }

    return NextResponse.json({
      success: true,
      message: 'Program updated successfully',
      data: programs[programIndex]
    })

  } catch (error) {
    console.error('Update program error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest, { params }: RouteParams) {
  try {
    const { id } = await params
    const programId = parseInt(id)

    if (isNaN(programId)) {
      return NextResponse.json(
        { error: 'Invalid program ID' },
        { status: 400 }
      )
    }

    const programIndex = programs.findIndex(p => p.id === programId)

    if (programIndex === -1) {
      return NextResponse.json(
        { error: 'Program not found' },
        { status: 404 }
      )
    }

    // Remove program
    programs.splice(programIndex, 1)

    return NextResponse.json({
      success: true,
      message: 'Program deleted successfully'
    })

  } catch (error) {
    console.error('Delete program error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
