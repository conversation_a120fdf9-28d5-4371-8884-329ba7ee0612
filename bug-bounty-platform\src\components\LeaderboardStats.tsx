import { Trophy, Target, TrendingUp, Users } from 'lucide-react'

export default function LeaderboardStats() {
  const stats = [
    {
      icon: Trophy,
      title: 'Top Earner This Month',
      value: '$25,000',
      subtitle: '<PERSON> (@alexsec)',
      color: 'text-yellow-600 bg-yellow-50'
    },
    {
      icon: Target,
      title: 'Most Bugs Found',
      value: '47',
      subtitle: '<PERSON> (@sarahj)',
      color: 'text-blue-600 bg-blue-50'
    },
    {
      icon: TrendingUp,
      title: 'Highest Streak',
      value: '15 days',
      subtitle: '<PERSON> (@rajsec)',
      color: 'text-green-600 bg-green-50'
    },
    {
      icon: Users,
      title: 'New Researchers',
      value: '234',
      subtitle: 'Joined this month',
      color: 'text-purple-600 bg-purple-50'
    }
  ]

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12">
      {stats.map((stat, index) => {
        const IconComponent = stat.icon
        return (
          <div key={index} className="card card-hover p-8 group bg-gradient-to-br from-white to-amber-50 border-amber-100">
            <div className={`inline-flex items-center justify-center w-16 h-16 rounded-2xl ${stat.color} mb-6 group-hover:scale-110 transition-transform duration-300`}>
              <IconComponent className="h-8 w-8" />
            </div>
            <h3 className="text-sm font-medium text-gray-500 mb-2">{stat.title}</h3>
            <p className="text-3xl font-black text-gray-900 mb-2">{stat.value}</p>
            <p className="text-sm text-gray-600 font-medium">{stat.subtitle}</p>
          </div>
        )
      })}
    </div>
  )
}
