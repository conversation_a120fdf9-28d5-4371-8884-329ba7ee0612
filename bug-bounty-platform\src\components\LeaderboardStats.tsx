import { Trophy, Award, DollarSign, Target, TrendingUp, Users } from 'lucide-react'

export default function LeaderboardStats() {
  const stats = [
    {
      icon: Trophy,
      title: 'Top Earner This Month',
      value: '$25,000',
      subtitle: '<PERSON> (@alexsec)',
      color: 'text-yellow-600 bg-yellow-50'
    },
    {
      icon: Target,
      title: 'Most Bugs Found',
      value: '47',
      subtitle: '<PERSON> (@sarahj)',
      color: 'text-blue-600 bg-blue-50'
    },
    {
      icon: TrendingUp,
      title: 'Highest Streak',
      value: '15 days',
      subtitle: '<PERSON> (@rajsec)',
      color: 'text-green-600 bg-green-50'
    },
    {
      icon: Users,
      title: 'New Researchers',
      value: '234',
      subtitle: 'Joined this month',
      color: 'text-purple-600 bg-purple-50'
    }
  ]

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      {stats.map((stat, index) => {
        const IconComponent = stat.icon
        return (
          <div key={index} className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className={`inline-flex items-center justify-center w-12 h-12 rounded-lg ${stat.color} mb-4`}>
              <IconComponent className="h-6 w-6" />
            </div>
            <h3 className="text-sm font-medium text-gray-500 mb-1">{stat.title}</h3>
            <p className="text-2xl font-bold text-gray-900 mb-1">{stat.value}</p>
            <p className="text-sm text-gray-600">{stat.subtitle}</p>
          </div>
        )
      })}
    </div>
  )
}
